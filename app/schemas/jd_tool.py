from typing import List
from pydantic import BaseModel, Field

class Guardrail(BaseModel):
    classification: str= Field(description="Classification as either True or False")

# class JobDescription(BaseModel):
    # job_description: str = Field(description="Job Description in pure HTML format")

class Data(BaseModel):
    location: str = Field(description="Location of the job, remote, hybrid, onsite (place) etc")
    role_title: str = Field(description="Role title")
    role_type: str = Field(description="Type of job, fulltime, part-time, contract, internship etc")
    about_the_role: str= Field(description="A paragraph on what the role is about")
    key_responsibilities: List[str] = Field(description="List of key responsibilities")
    kpis: List[str] = Field(description="List of key performance indicators")
    qualifications: List[str] = Field(description="List of qualifications")
    preferred_skills: List[str] = Field(description="List of preferred skills")
    what_we_offer: List[str] = Field(description="List of what we offer")

class JobDescription(BaseModel):
    job_description: Data
class JobDescription(BaseModel):
    job_description: str = Field(description="Job Description in pure HTML format")

class KPIs(BaseModel):
    kpis: List[str] = Field(description="List of top 10 KPIs generated for the given role.")
