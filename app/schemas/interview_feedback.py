from pydantic import BaseModel
from schemas.state import StateSchema
from typing import Optional
from typing import Optional, Dict, Any, List
from enum import Enum
from schemas.user import User, UserDetail, SuitableUser


class IntervewFeedbackSchema(BaseModel):
    interview_id: int
    requisition_id: int
    user_id: int
    feedback: Optional[Dict[str, Any]] = None
    score: Optional[float] = None

    class Config:
        orm_mode = True


class InterviewFeedbackListSchema(IntervewFeedbackSchema):
    id: int
    user: SuitableUser

class InterviewFeedbackReadSchema(IntervewFeedbackSchema):
    id: int
    user: UserDetail


class AIgeneratedcontentpercentage(BaseModel):
    percentage: str
    reason: str


class LocationFilter(BaseModel):
    country_id: Optional[int] = None
    city_id: Optional[int] = None

class CandidateFilterRequest(BaseModel):
    requisition_id: int
    page: int = 1
    per_page: int = 10
    location: Optional[LocationFilter] = None
    filters: Optional[List[str]] = []
    q: Optional[str] = None
    city_id: Optional[int] = None
    country_id: Optional[int] = None
    most_recent: Optional[bool] = None

# Response models for better documentation
class PaginationInfo(BaseModel):
    total: int
    last_page: int
    page: int
    per_page: int

class CandidateFilterResponse(BaseModel):
    pagination: PaginationInfo
    items: list
