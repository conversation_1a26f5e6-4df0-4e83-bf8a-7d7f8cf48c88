from pydantic import BaseModel
from datetime import datetime
from typing import Optional

class FeedbackSchema(BaseModel):
    id: int
    interview_id: int
    user_id: int
    rating: float
    comments: str
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class FeedbackCreateSchema(BaseModel):
    interview_id: Optional[int]
    rating: float
    comments: Optional[str] = None

    