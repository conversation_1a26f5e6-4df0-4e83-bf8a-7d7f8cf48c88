from celery_config import celery_app
from database import sessionLocal
import logging
from services.requisition.recommendation import CandidateRecommendation

from services.external.opensearch import OpenSearchServiceClass
from dependencies import OPENSEARCH_RECRUITER_REQUISITION_INDEX,RECRUITER_OUTREACH_MAIL_FROM,ENVIRONMENT,RECRUITER_APP_HOST
from helper.html_stripper import strip_html_tags
from services.requisition.requisition import RequisitionServiceClass
from services.requisition.requisition_similarity import RequisitionSimilarity
from schemas.vapi import RecruiterRequisitionCreateSchema
from services.notification.email import send_email_with_celery
from dependencies import S3_URL

logger = logging.getLogger(__name__)
requisition_service_object = RequisitionServiceClass()
candidate_recommendation_service_object = CandidateRecommendation()
requisition_similarity_service_object = RequisitionSimilarity()

@celery_app.task(name="create_recruiter_requisition", queue="recruiter_requsition_conversation_queue")
def create_recruiter_requisition(organization_id: int, document_id:int,user_id: int, profession_id: int, requisition_data: dict,recruiter_info:dict,is_recruiter_exists: bool):
    try:
        open_search_service_object = OpenSearchServiceClass(OPENSEARCH_RECRUITER_REQUISITION_INDEX)
        db = sessionLocal()
        logger.info(f"Create recruiter requisition task started")
        recruiter_req_create_data=RecruiterRequisitionCreateSchema(**requisition_data)
        requisition=requisition_service_object.create_requisition_from_recruiter_conversation(organization_id, user_id, profession_id, recruiter_req_create_data,db,recruiter_info)
        eval_response = candidate_recommendation_service_object.create_evaluation_criteria(
            requisition["id"], db)
        vector_embeddings_response = requisition_similarity_service_object.store_embeddings(requisition["id"], db)
        response = candidate_recommendation_service_object.create_recommended_candidates_from_similar_requisitions(requisition["id"], db)
        open_search_service_object.update_status_in_opensearch(document_id)
        if is_recruiter_exists:
            send_email_with_celery(subject=
                "Your Job Role Is Live - Here's What's Next",
                email_to=recruiter_info.get("email"),
                data={
                    "first_name": recruiter_info.get("first_name"),
                    "email": recruiter_info.get("email"),
                    "url": f"{S3_URL}/images/mailbox.png",
                    "recruiter_portal": RECRUITER_APP_HOST
                },
                html_template="existing_recruiter_requisition_created.html",
                sender_email=RECRUITER_OUTREACH_MAIL_FROM
            )

        else:
            send_email_with_celery(subject=
                "Your Job Role Is Live - Here's What's Next",
                email_to=recruiter_info.get("email"),
                data={
                    "first_name": recruiter_info.get("first_name"),
                    "email": recruiter_info.get("email"),
                    "password": recruiter_info.get("password"),
                    "url": f"{S3_URL}/images/mailbox.png",
                    "recruiter_portal": RECRUITER_APP_HOST
                },
                html_template="new_recruiter_requisition_created.html",
                sender_email=RECRUITER_OUTREACH_MAIL_FROM
            )

        logger.info(f"Create recruiter requisition task completed")
        return True

    except Exception as e:
        logger.error(f"Error in create_recruiter_requisition task: {e}")

    finally:
        db.close()