from services.external.bedrock import BedrockService
from fastapi import Request, HTTPException
import fitz
from pprint import pprint
from services.filemanager.filemanager import FileManagerClass
from services.external.openai import OpenAIServiceClass
from services.qc_automation.qc_automation import QCAutomationClass
from services.user.user import UserServiceClass
from services.parser.text_extractor_service import TextExtractorService
from helper.helper import (
    get_user_education_object,
    get_user_experience_object,
)
from dependencies import (
    AWS_RESUME_FOLDER,
)
from celery_config import celery_app
import json
import logging
from models.country import Country
from database import sessionLocal, get_mongodb_client
from sqlalchemy.orm import Session
from decimal import Decimal
from services.location_service import LocationService
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

@celery_app.task(name="upload-user-resume")
def upload(request, file_path, file_name_object):
    request = type(
        "Request",
        (object,),
        {
            "state": type(
                "State", (object,), {"user": type("User", (object,), {"id": request})()}
            )()
        },
    )
    logger.info("inside resume..")
    db: Session = sessionLocal()
    mongodb: Session = get_mongodb_client()
    text_extractor_service = TextExtractorService()
    user_service_object = UserServiceClass()
    location_service_object = LocationService()
    qc_automation_service = QCAutomationClass()

    # if not text_extractor_service.is_supported_extension(file.filename):
    # raise e
    try:
        logger.info("Upload Resume Service Started")
        file_manager_object = FileManagerClass()

        logger.info(f"File Path {file_path}")
        # Get the file content from S3
        extracted_text = file_manager_object.read_s3_file(file_path)

        # Check if file path is valid
        if not file_path:
            logger.error("File path is invalid")
            return None

        logger.info(f"Extracted Text send to service")
        # Extract text from the file
        parser_text = text_extractor_service.extract_text_from_content(
            extracted_text["content"], extracted_text["extension"]
        )
        logger.info(f"Parser Text Completed")
        logger.info(parser_text)
        llmResponse=None

        # Check if parser text is valid
        if not parser_text:
            logger.info("Parser text is invalid, scanning resume now...")
            llmResponse = qc_automation_service.scan_resume_and_extract_data(file_path)
            llmResponse=llmResponse['response']
        
        else:
            logger.info("Parser text valid...")
            llmResponse = BedrockService().analyze_resume_from_text(parser_text)
        
        logger.info(f"LLM Response Completed")   

        total_experience = llmResponse["total_experience"]

        city = llmResponse.get("address", {}).get("city", None)
        country_code = llmResponse.get("address", {}).get("country", None)
        state = llmResponse.get("address", {}).get("state", None)
        logger.info(
            "================================ LLM Response ================================ "
        )
        # update user education data
        user_education_object = get_user_education_object(llmResponse["education"], db)
        logger.info(
            "================================ User Education Object ================================ "
        )
        logger.info(str(user_education_object))
        update_education = user_service_object.update_education(
            user_education_object, request, db
        )
        logger.info(
            "================================ User Education Model ================================ "
        )
        logger.info(str(update_education))
        # update user experience
        user_experience_object = get_user_experience_object(
            llmResponse["work_experience"], db
        )
        logger.info(
            "================================ User Experience Object ================================ "
        )
        logger.info(str(user_experience_object))
        user_experience = user_service_object.update_experience(
            user_experience_object, request, db
        )
        logger.info(
            "================================ User Experience Model ================================ "
        )
        logger.info(str(user_experience))
        # update user skills
        skill = user_service_object.create_update_user_skill(
            llmResponse["skills"], request, db
        )

        data = location_service_object.get_location(city, country_code, state, db)

        logger.info("No issues found in country/city for given user")

        user_object_to_update = {
            "resume_link": (
                file_name_object
                if isinstance(file_name_object, str)
                else json.dumps(file_name_object)
            ),
            "total_experience": (
                float(total_experience)
                if isinstance(total_experience, Decimal)
                else total_experience
            ),
            "city_id": (
                int(data["city_id"])
                if isinstance(data["city_id"], Decimal)
                else data["city_id"]
            ),
            "country_id": (
                int(data["country_id"])
                if isinstance(data["country_id"], Decimal)
                else data["country_id"]
            ),
            "state_id": (
                int(data["state_id"])
                if isinstance(data["state_id"], Decimal)
                else data["state_id"]
            ),
            "location": data["location"],
            "about": str(llmResponse["summary"]),
            "linkedin": str(llmResponse.get('linkedin', None))
        }

        # update user with resume link
        user_object = user_service_object.update_user_resume_detail(
            user_object_to_update, request, db
        )

        # delete file in temp folder
        file_manager_object.delete_temp_file(file_path)
                
        return None
    except Exception as e:
        logger.error("Error in uploading resume")
        logger.error(str(e))
        return None
    finally:
        logger.info(f"Upload Resume Service Ended")
        db.close()
        return True
