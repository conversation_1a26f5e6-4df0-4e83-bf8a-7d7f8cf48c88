from sqlalchemy import Column, BigInteger, Float, Text, TIMESTAMP, func, ForeignKey, Integer
from database import Base
from sqlalchemy.orm import relationship

class Feedback(Base):
    __tablename__ = "feedback"
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    interview_id = Column(Integer, nullable=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    rating = Column(Float, nullable=False, default=0)
    comments = Column(Text, nullable=False, default=0)
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp())
    updated_at = Column(
        TIMESTAMP,
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
    )


    # user = relationship("User")
    # interview = relationship("interviews")