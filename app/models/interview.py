from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>n,
    <PERSON><PERSON><PERSON>,
    Integer,
    String,
    BigInteger,
    Date,
    TIMESTAMP,
    Text,
    func
)
from sqlalchemy.orm import relationship
from database import Base
from database import Base


class Interview(Base):
    __tablename__ = "interviews"
    id = Column(BigInteger, primary_key=True)
    user_id = Column(BigInteger, ForeignKey("users.id"), index=True)
    code = Column(String)
    scheduled_at = Column(TIMESTAMP, server_default=func.current_timestamp())
    time_zone = Column(String)
    duration = Column(Integer, nullable=True)
    profession_id = Column(Integer, nullable=True)
    requisition_id = Column(Integer, default=0, nullable=True)
    status = Column(Integer, default=0, nullable=True)
    send_reminder = Column(Integer, default=0, nullable=True)
    send_half_hour_reminder = Column(Integer, default=0, nullable=True)
    state_machine_nodes = Column(Text, nullable=True)
    
    INTERVIEW_PENDING = 0
    INTERVIEW_STARTED = 1
    INTERVIEW_RESCHEDULED = 2
    INTERVIEW_COMPLETED = 3
    INTERVIEW_CLOSED = 4
    INTERVIEW_CLOSE_FAILED = 5
    INTERVIEW_CANCELLED = 6

    
    INTERVIEW_STATUS_TEXT = {
        INTERVIEW_PENDING: "Pending",
        INTERVIEW_STARTED: "Started",
        INTERVIEW_RESCHEDULED: "Rescheduled",
        INTERVIEW_COMPLETED: "Completed",
        INTERVIEW_CLOSED: "Closed",
        INTERVIEW_CLOSE_FAILED: "Close Failed"
    }

    def statusText(self):
        if self.status == 0:
            return "Pending"
        elif self.status == 1:
            return "Started"
        elif self.status == 2:
            return "Rescheduled"
        elif self.status == 3:
            return "Completed"
        
    user = relationship("User", back_populates="user_interview")
