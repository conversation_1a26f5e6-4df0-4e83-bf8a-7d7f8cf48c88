from sqlalchemy import Column, Integer, String, Text, BigInteger, Date, TIMESTAMP, func, ForeignKey, JSON
from database import Base
from sqlalchemy.orm import relationship


class Requisition(Base):
    __tablename__ = "requisitions"
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id'), index=True)
    title = Column(String(250))
    description = Column(Text, nullable=True)
    salary_range = Column(Text, nullable=True)
    location_type = Column(String(250), nullable=True)
    job_type = Column(String(250), nullable=True)
    close_date = Column(Date, nullable=True)
    status = Column(Integer, default=1)
    job_link = Column(Text, nullable=True)
    level = Column(String(250), nullable=True)
    fine_tuning_status = Column(Integer, default=0)
    requirements = Column(Text, nullable=True)
    responsibilities = Column(Text, nullable=True)
    salary_details = Column(Text, nullable=True)
    llm_requistion_details = Column(Text, nullable=True)
    binary_requirements=Column(Text,nullable=True)
    long_description = Column(Text, nullable=True)
    show_salary = Column(Integer, default=0)
    evaluation_criteria = Column(JSON, nullable=True)

    city_id = Column(Integer, ForeignKey('cities.id'), index=True)
    state_id = Column(Integer, index=True, nullable=True)
    country_id = Column(Integer, index=True, nullable=True)
    
    organization_id = Column(Integer, ForeignKey(
        'organizations.id'), index=True)
    profession_id = Column(Integer, ForeignKey('professions.id'), index=True)

    city = relationship("City")
    organization = relationship("Organization")
    requisition_skills = relationship(
        'RequisitionSkill', back_populates='requisition')
    user_requisition = relationship(
        "UserRequisition", back_populates='requisition')

    ACTIVE = 1
    INACTIVE = 0
