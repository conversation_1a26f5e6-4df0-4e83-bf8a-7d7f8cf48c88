from sqlalchemy import (
    TIMESTAMP,
    Boolean,
    Column,
    Foreign<PERSON>ey,
    Integer,
    String,
    BigInteger,
    Date,
    DateTime,
)
import sqlalchemy as sa
from sqlalchemy.orm import relationship
from datetime import datetime

import sqlalchemy as sa
import sqlalchemy as sa
from datetime import datetime

from database import Base


class User(Base):
    __tablename__ = "users"
    id = Column(BigInteger, primary_key=True)
    first_name = Column(String)
    last_name = Column(String)
    email = Column(String, nullable=False)
    password = Column(String, nullable=True)
    profession_id = Column(Integer)
    phone = Column(String, nullable=True)
    nationality = Column(String, nullable=True)
    date_of_birth = Column(Date, nullable=True)
    gender = Column(String, nullable=True)
    city_id = Column(Integer, ForeignKey(
        'cities.id'), nullable=True, index=True)
    profile_pic = Column(String, nullable=True)
    resume_link = Column(String, nullable=True)
    status = Column(Integer, default=0)
    expected_salary = Column(String, nullable=True)
    location_preferences = Column(String, nullable=True)
    notice_period = Column(String, nullable=True)
    total_experience = Column(String, nullable=True)
    about = Column(String, nullable=True)
    is_guest = Column(Boolean, default=False)
    salary_negotiable = Column(Boolean, nullable=True, default=True)
    linkedin_url = Column(String, nullable=True, default=None)
    opensearch_status = Column(Integer, default=0)

    created_at = Column(
        sa.TIMESTAMP, server_default=sa.func.current_timestamp())
    country_id = Column(Integer, nullable=True)
    location = Column(String, nullable=True)
    state_id = Column(Integer, nullable=True)
    # updated_at=(sa.TIMESTAMP, server_default=sa.func.current_timestamp()),

    educations = relationship("UserEducation", back_populates='user')
    experiences = relationship(
        'UserExperience', back_populates='user', lazy='joined')
    projects = relationship(
        'UserProject', back_populates='user', lazy='joined')
    experiences = relationship(
        'UserExperience', back_populates='user', lazy='joined')
    projects = relationship(
        'UserProject', back_populates='user', lazy='joined')
    skills = relationship('UserSkill', back_populates='user', lazy='joined')
    user_interview_feedbacks = relationship('InterviewFeedback', back_populates='user', lazy='joined')
    shortlisted_candidate = relationship('ShortlistedCandidate', back_populates='user', lazy='joined')
    user_interview = relationship('Interview', back_populates='user', lazy='joined')

    qc_automation_entries = relationship("UserProfileRevisions", back_populates="user", lazy='joined')
