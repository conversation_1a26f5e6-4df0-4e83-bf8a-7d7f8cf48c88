from sqlalchemy import and_, or_
from datetime import datetime
from typing import List, Optional, Dict, Any, Union
import math
from fastapi.encoders import jsonable_encoder
from models.interview import Interview

def create_interview_status_filters(filter_types: Optional[List[str]] = None) -> List[Any]:
    """
    Creates SQLAlchemy filter conditions for interview statuses based on the provided filter types.
    
    Args:
        filter_types: List of filter types to apply. If None or empty, defaults to ['scheduled'].
                     Possible values: 'all', 'scheduled', 'completed', 'pending', 'missed'
    
    Returns:
        List of SQLAlchemy filter conditions to be used with query.filter(or_(*conditions))
    """
    if not filter_types:
        filter_types = ['scheduled']
    
    # If 'all' is present, return an empty list (no filtering)
    if 'all' in filter_types:
        return []
    
    filter_conditions = []
    for filter_type in filter_types:
                # Strip whitespace in case there are spaces after commas
        filter_type = filter_type.strip()
        if filter_type == 'scheduled':
            filter_conditions.append(Interview.status == Interview.INTERVIEW_STARTED)
        elif filter_type == 'completed':
            filter_conditions.append(Interview.status == Interview.INTERVIEW_CLOSED)
        elif filter_type == 'pending':
            filter_conditions.append(
                and_(
                    Interview.status == Interview.INTERVIEW_PENDING,
                    Interview.scheduled_at > datetime.now()
                )
            )
        elif filter_type == 'missed':
            filter_conditions.append(
                and_(
                    Interview.status == Interview.INTERVIEW_PENDING,
                    Interview.scheduled_at < datetime.now()
                )
            )
        elif filter_type == 'upcoming': #confirm it's condition becoz it is as similar to pending
            filter_conditions.append(
                and_(
                    Interview.status == Interview.INTERVIEW_PENDING,
                    Interview.scheduled_at > datetime.now()
                ))
            
    return filter_conditions

def apply_interview_filters(query, filter_types: Optional[List[str]] = None):
    filter_conditions = create_interview_status_filters(filter_types)
    # Apply the filters as OR conditions if there are any
    if filter_conditions:
        query = query.filter(or_(*filter_conditions))
    
    return query

def paginate_results(query, page: int, per_page: int, encode_items: bool = True) -> Dict[str, Any]:
    """
    Paginates a SQLAlchemy query and returns formatted results with pagination metadata.
    
    Args:
        query: SQLAlchemy query to paginate
        page: Page number (starting from 1)
        per_page: Number of items per page
    
    Returns:
        Dictionary with pagination metadata and results
    """
    
    total = query.count()
    items = query.offset((page - 1) * per_page).limit(per_page).all()
    
    return {
        "pagination": {
            "total": total,
            "last_page": math.ceil(total / per_page),
            "page": page,
            "per_page": per_page,
        },
        "items": jsonable_encoder(items) if encode_items else items,
    }