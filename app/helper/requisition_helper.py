from models.requisition import Requisition
from models.user_requisition import UserRequisition
from models.interview_feedback import InterviewFeedback
from models.shortlisted_candidate import ShortlistedCandidate
from models.interview import Interview
from dependencies import CANDIDATE_THRESHOLD_SCORE
import logging
from sqlalchemy import func, case

logger = logging.getLogger(__name__)


def requisition_interviews_listing_statuses():
    return {"upcoming", "pending", "missed"}


def get_requisition_stats(requisition_id, db):
    try:
        # get number of candidates applied for the requisition
        total_candidates = (
            db.query(UserRequisition)
            .filter(
                UserRequisition.requisition_id == requisition_id,
                UserRequisition.is_applied == 1,
                # UserRequisition.score == None,
            )
            .count()
        )
        interview_stats_query = (
            db.query(
                func.count(
                    case((Interview.status != Interview.INTERVIEW_RESCHEDULED, 1))
                ).label("interviews_scheduled"),
                func.count(
                    case((Interview.status == Interview.INTERVIEW_CLOSED, 1))
                ).label("interviews_completed"),
                func.count(
                    case((Interview.status == Interview.INTERVIEW_PENDING, 1))
                ).label("interviews_pending"),
            )
            .filter(Interview.requisition_id == requisition_id)
            .subquery()
        )
        feedback_stats_query = (
            db.query(
                func.count(
                    case(
                        (
                            (InterviewFeedback.score >= CANDIDATE_THRESHOLD_SCORE)
                            & (
                                InterviewFeedback.status
                                == InterviewFeedback.STATUS_APPROVED
                            ),
                            1,
                        )
                    )
                ).label("shortlisted_candidates")
            )
            .filter(InterviewFeedback.requisition_id == requisition_id)
            .subquery()
        )

        final_stats = db.query(
            interview_stats_query.c.interviews_scheduled,
            interview_stats_query.c.interviews_completed,
            interview_stats_query.c.interviews_pending,
            feedback_stats_query.c.shortlisted_candidates,
        ).one()
        interviews_scheduled = final_stats.interviews_scheduled
        interviews_completed = final_stats.interviews_completed
        interviews_pending = final_stats.interviews_pending
        shortlisted_candidates = final_stats.shortlisted_candidates
        return {
            "total_candidates": total_candidates,
            "interviews_scheduled": interviews_scheduled,
            "interviews_completed": interviews_completed,
            "interviews_pending": interviews_pending,
            "shortlisted_candidates": shortlisted_candidates,
        }
    except Exception as e:
        logger.error(
            f"Error in get_requisition_stats: {e}, requisition_id: {requisition_id}"
        )
        return {
            "total_candidates": 0,
            "interviews_scheduled": 0,
            "interviews_completed": 0,
            "interviews_pending": 0,
            "shortlisted_candidates": 0,
        }
