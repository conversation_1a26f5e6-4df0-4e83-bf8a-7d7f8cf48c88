from datetime import datetime, timezone
from services.school import SchoolServiceClass
from services.city import CityServiceClass
from services.country import CountryServiceClass
from services.organization.organization import OrganizationServiceClass
from types import SimpleNamespace
from models.country import Country
from models.city import City
from datetime import datetime
from dateutil import parser
from models.city import City
from dependencies import SECRET_KEY, ALGORITHM
from models.user import User
import jwt
from database import get_db
from sqlalchemy.orm import Session
import pytz
from models.shortlisted_candidate import ShortlistedCandidate
from models.recommended_candidates import RecommendedCandidates
from models.interview_feedback import InterviewFeedback
from dependencies import CANDIDATE_THRESHOLD_SCORE,RECOMMENDED_CANDIDATE_THRESHOLD_SCORE

def format_person_data(person_data: dict) -> str:
    summary = []

    # Basic Info
    name = f"Candidate ID: {person_data.get('person_id', '')}"
    experience = f"Total Experience: {person_data.get('total_experience', '')} years"
    salary = f"Expected Salary: {person_data.get('expected_salary', '')}"
    notice = f"Notice Period: {person_data.get('notice_period', '')}"
    location = f"Location Preference: {person_data.get('location_preferences', '')}"

    summary.extend([name, experience, salary, notice, location])

    # Education
    education_entries = person_data.get('education', [])
    if education_entries:
        edu = education_entries[0]
        start_date = (edu.get('start_date') or '')
        end_date = (edu.get('end_date') or '')
        education_summary = (
            f"Education: {edu.get('degree', '')} in {edu.get('field', '')} from {edu.get('school', '')} "
            f"({start_date} - {end_date})"
        )
        summary.append(education_summary)

    # Work Experience
    work_entries = person_data.get('work_experience', [])
    if work_entries:
        work_summary = "Work Experience:"
        for job in work_entries:
            org = job.get('organization', '') or ''
            title = job.get('job_title', '') or ''
            loc = job.get('location_type', '') or ''
            from_date = (job.get('from_date') or '')
            to_date = (job.get('to_date') or 'Present')
            years = job.get('relevant_experience') or 0
            work_summary += f"\n- {title} at {org} ({loc}, {from_date} – {to_date}) | {years:.2f} yrs relevant experience"
        summary.append(work_summary)

    # Skills
    skills = person_data.get('skills', [])
    if skills:
        summary.append(f"Skills: {', '.join(skills)}")

    # Address
    address = person_data.get('address', {}) or {}
    city = address.get('city', '') or ''
    country = address.get('country', '') or ''
    if city or country:
        summary.append(f"Address: {city}, {country}")

    return '\n\n'.join(summary)


def get_location(education):
    city = education.get("city", "").strip()
    country = education.get("country", "").strip()

    # Ignore empty strings and "null" values
    valid_city = city if city and city.lower() != "null" else None
    valid_country = country if country and country.lower() != "null" else None

    # If both values are the same (case-insensitive), use only one
    if valid_city and valid_country and valid_city.lower() == valid_country.lower():
        location = valid_city
    else:
        location = ", ".join(filter(None, [valid_city, valid_country]))

    return location


def get_user_education_object(user_education_array, db):
  try:
      # init empty array
      user_educations = []
      # call service object
      school_service = SchoolServiceClass()
      city_service = CityServiceClass()
      country_service = CountryServiceClass()
      for education in user_education_array:
          country_id = 0
          city_id = 0
          school_id = 0

          # Update school id if school exist
          if education["school"] is not None and education["school"] != "":
              school_input = {
                  "name": education["school"],
                  "logo": None,
                  "city_id": city_id,
                   "location":get_location(education)
              }
              # Convert to object
              school_input = SimpleNamespace(**school_input)
              school_object = school_service.add_school(school_input, db)
              school_id = school_object.id
          user_educations.append(
              {
                  "school_id": school_id,
                  "degree": education.get("degree", None),
                  "field": education.get("field", None),
                  "start_date": str_to_date(education.get("start_date", None)),
                  "end_date": (
                      str_to_date(education.get("end_date", None))
                      if education.get("end_date", None)
                      and str_to_date(education.get("end_date", None))
                      else None
                  ),
                  "grade": education.get("grade", None),
                  "description": education.get("description", None),
                  # "city_id": city_id,
                   "location":get_location(education)
              }
          )




      # Convert each dictionary in the list to a SimpleNamespace object
      user_educations = [
          SimpleNamespace(**user_education) for user_education in user_educations
      ]
      return user_educations
  except Exception as e:
      return e




def get_user_education_object_scraping_dog(user_education_array, db):
  try:
      # init empty array
      user_educations = []
      # call service object
      school_service = SchoolServiceClass()
      city_service = CityServiceClass()
      country_service = CountryServiceClass()
      for education in user_education_array:
          country_id = 0
          city_id = 0
          school_id = 0

          # Update school id if school exist
          school_input = {
            "name": education["college_name"],
            "logo": education["college_image"],
            "city_id": city_id,
            "location":get_location(education)
         }
          
          college_duration = education.get('college_duration', "")
          
            # Split the string into start and end years
          start_year, end_year = college_duration.split(' - ')

          start_date = datetime.strptime(f"{start_year}-01-01", "%Y-%m-%d").date()
          end_date = datetime.strptime(f"{end_year}-01-01", "%Y-%m-%d").date()
          school_input = SimpleNamespace(**school_input)
          school_object = school_service.add_school(school_input, db)
          school_id = school_object.id
          user_educations.append(
              {
                  "school_id": school_id,
                  "degree": education.get("college_degree", None),
                  "field":education.get("college_degree_field") or "",
                  "start_date": start_date,
                  "end_date": end_date,
                  "grade": education.get("grade", None),
                  "description": education.get("description", None),
                  # "city_id": city_id,
                   "location":get_location(education)
              }
          )

      # Convert each dictionary in the list to a SimpleNamespace object
      user_educations = [
          SimpleNamespace(**user_education) for user_education in user_educations
      ]
      return user_educations
  except Exception as e:
      return e


def calculate_experience_years(from_date, to_date):
    if not from_date:
        return 0
    to_date = to_date or datetime.now(timezone.utc).date()
    return round((to_date - from_date).days / 365.0, 2)

def get_user_experience_object_scraping_dog(experience_data_array, db):
    print(experience_data_array)
    try:
        user_experiences = []
        organization_service = OrganizationServiceClass()
        city_service = CityServiceClass()
        country_service = CountryServiceClass()

        for experience, domain, linkedinID in experience_data_array:
            country_id = 0
            city_id = 0

            if experience.get("company_name"):
                organization_input = {
                    "name": experience.get("company_name", ""),
                    "logo": experience.get("company_image", ""),
                    "city_id": city_id,
                    "location": get_location(experience),
                    "linkedin_id": linkedinID,
                    "domain": domain,
                }
                organization_object = organization_service.add_organization(
                    organization_input, db
                )
                organization_id = organization_object.id
            else:
                organization_id = None

            user_experiences.append(
                {
                    "organization_id": organization_id,
                    "job_title": experience.get("position", ""),
                    "emp_type": (
                        "Full-Time" if not experience.get("emp_type") else experience["emp_type"]
                    ),
                    "from_date": str_to_date(experience.get("starts_at")),
                    "to_date": (
                        str_to_date(experience.get("ends_at"))
                        if experience.get("ends_at") and str_to_date(experience["ends_at"])
                        else None
                    ),
                    "description": experience.get("summary", ""),
                    "location": get_location(experience),
                    "location_type": experience.get("location_type", ""),
                    "skills": [],
                }
            )

        return [SimpleNamespace(**ue) for ue in user_experiences]

    except Exception as e:
        print(e)
        raise e



def get_user_experience_object(user_experience_array, db):
  try:
      user_experiences = []
      organization_service = OrganizationServiceClass()
      city_service = CityServiceClass()
      country_service = CountryServiceClass()
      # return user_experience_array
      for experience in user_experience_array:
          country_id = 0
          city_id = 0
          organization_id=0

          # Update school id if organization exist
          if (
              experience["organization"] is not None
              and experience["organization"] != ""
          ):
              organization_input = {
                  "name": experience["organization"],
                  "logo": None,
                  "city_id": city_id,
                  "location":get_location(experience)

              }
              organization_object = organization_service.add_organization(
                  organization_input, db
              )
              organization_id = organization_object.id




          user_experiences.append(
              {
                  "organization_id": organization_id,
                  "job_title": experience["job_title"],
                  "emp_type": (
                      "Full-Time"
                      if experience["emp_type"] == ""
                      else experience["emp_type"]
                  ),
                  "from_date": str_to_date(experience["start_date"]),
                  "to_date": (
                      str_to_date(experience["end_date"])
                      if experience["end_date"]
                      and str_to_date(experience["end_date"])
                      else None
                  ),
                  "description": experience["description"],
                  # "city_id": city_id,
                  "location":get_location(experience),
                  "location_type": experience["location_type"],
                  "skills": [],
              }
          )
      # Convert each dictionary in the list to a SimpleNamespace object
      user_experiences = [
          SimpleNamespace(**user_experience) for user_experience in user_experiences
      ]
      return user_experiences
  except Exception as e:
      print(e)
      raise e




def str_to_date(datestr):
   try:
       date = parser.parse(datestr, fuzzy=True)
       return date.strftime("%Y-%m-%d")
   except ValueError:
       return None
   except Exception as e:
       print(e)
       raise e




def get_authenticate_token(request):
   try:
       authorization: str = request.headers.get("Authorization")
       token = None


       if authorization and authorization.startswith("Bearer "):
           token = authorization.split(" ")[1]
       return token
   except Exception as e:
       print(e)
       raise None




def get_authenticate_user(request):
   try:
       token = get_authenticate_token(request)
       user = None
       if token:
           try:
               payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
               userEmail = payload.get("sub")
               if userEmail:
                   db: Session = next(get_db())
                   user = db.query(User).filter(User.email == userEmail).first()
           except jwt.ExpiredSignatureError:
               return None
           except jwt.InvalidTokenError:
               return None
       if not user:
           return None
       request.state.user = user
       return user
   except Exception as e:
       raise e




def convert_local_to_utc(local_time_str, time_zone="Asia/Dubai"):
   try:
       # Parse the local time string into a datetime object
       local_time = datetime.strptime(local_time_str, "%Y-%m-%d %H:%M:%S")
      
       # Set the local timezone
       local_tz = pytz.timezone(time_zone)
      
       # Localize the datetime object to the local timezone
       local_time = local_tz.localize(local_time)
      
       # Convert the localized time to UTC
       utc_time = local_time.astimezone(pytz.utc)
      
       return utc_time.strftime("%Y-%m-%d %H:%M:%S")
   except Exception as e:
       raise e


def convert_utc_to_local(utc_date, time_zone="Asia/Dubai"):
   try:
       if isinstance(utc_date, str):
           utc_date = parser.parse(utc_date)


       # Set timezone info to UTC if it's naive
       if utc_date.tzinfo is None:
           utc_date = pytz.utc.localize(utc_date)


       # Convert to the desired timezone
       # local_tz = pytz.timezone(time_zone)
       # local_date = utc_date.astimezone(local_tz)


       # Separate date and time
       date = utc_date.strftime("%Y-%m-%d")
       time = utc_date.strftime("%H:%M:%S")


       return {"date": date, "time": time}
   except Exception as e:
       raise e

def get_shortlisted_candidates_ids(requisition_id, db):
   try:
       shortlisted_candidates = (
           db.query(ShortlistedCandidate)
           .filter(
               ShortlistedCandidate.requisition_id == requisition_id,
           )
           .all()
       )
       shortlisted_candidates_ids = [
           candidate.candidate_id for candidate in shortlisted_candidates
       ]
       return shortlisted_candidates_ids
   except Exception as e:
       raise e


def parse_data_for_dropdown(data):
    try:
        parsed_data = []
        for row in data:
            parsed_data.append({"id": row.id, "name": row.name})
        return parsed_data
    except Exception as e:
        return []


def get_recommended_candidates_count(requisition_id,shortlisted_candidate_ids, db):
    try:
        recommended_candidates_count = (
            db.query(RecommendedCandidates)
            .filter(
                RecommendedCandidates.requisition_id == requisition_id,
                RecommendedCandidates.score >= RECOMMENDED_CANDIDATE_THRESHOLD_SCORE,
                RecommendedCandidates.user_id.notin_(shortlisted_candidate_ids),
                # RecommendedCandidates.satisfies_binary_requirements == True
            )
            .count()
        )
        return recommended_candidates_count
    except Exception as e:
        raise e

def get_interviewed_candidates_count(requisition_id,shortlisted_candidate_ids, db):
    try:
        interviewed_candidates_count = (
            db.query(InterviewFeedback)
            .filter(
                InterviewFeedback.requisition_id == requisition_id,
                InterviewFeedback.score >= CANDIDATE_THRESHOLD_SCORE,
                InterviewFeedback.status == InterviewFeedback.STATUS_APPROVED,
                InterviewFeedback.user_id.notin_(shortlisted_candidate_ids),
                InterviewFeedback.satisfies_binary_requirements == True,
            )
            .count()
        )
        return interviewed_candidates_count
    except Exception as e:
        raise e

