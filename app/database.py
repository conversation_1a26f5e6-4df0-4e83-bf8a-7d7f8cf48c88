from opensearchpy import OpenSearch
from sqlalchemy import create_engine
from pymongo import MongoClient
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from dependencies import (
    DB_USER,
    DB_PASSWORD,
    DB_HOST,
    DB_PORT,
    DB_NAME,
    DB_DRIVER,
    OP<PERSON><PERSON><PERSON>CH_HOST,
    OPEN<PERSON><PERSON>CH_PORT,
    OPENSEARCH_USER,
    OPEN<PERSON>ARCH_PASSWORD,
    MONGO_HOST,
    MONGO_PORT,
    MONGO_DATABASE,
    MONGO_USERNAME,
    MONGO_PASSWORD,
    MONGO_ADMIN_NODE,
    MONGO_SSL_CERT_PATH
)

SQLALCHEMY_DATABASE_URL = f"{DB_DRIVER}://{DB_USER}:{DB_PASSWORD}@{DB_HOST}/{DB_NAME}"

try:
    engine = create_engine(
        SQLALCHEMY_DATABASE_URL,
        pool_size=150,
        max_overflow=50,
        pool_timeout=30,
        pool_pre_ping=True,
        pool_recycle=1800,
    )
    print("Successfully connected to the database.")
except Exception as e:
    print("Failed to connect to the database: ", e)

sessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()


def get_db():
    db = sessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_opensearch_client():
    try:
        print("Connecting to OpenSearch...", OPENSEARCH_HOST, OPENSEARCH_PORT)
        opensearch_client = OpenSearch(
            hosts=[{"host": OPENSEARCH_HOST, "port": OPENSEARCH_PORT}],
            http_auth=(OPENSEARCH_USER, OPENSEARCH_PASSWORD),
            use_ssl=False,
            verify_certs=False,
            timeout=30,
            max_retries=3,
            retry_on_timeout=True,
            # ssl_assert_hostname=False,
            # ssl_show_warn=False
        )
        print("Connected to OpenSearch.")
        return opensearch_client
    except Exception as e:
        print("Error connecting to OpenSearch: ", e)

def close_opensearch_client(client):
    if client:
        client.transport.close()
        print("Closed OpenSearch connection.")

# MongoDB client configuration
def get_mongodb_client():
    try:

        print({
            "MONGO_HOST":MONGO_HOST,
            "MONGO_PORT":MONGO_PORT,
            "MONGO_DATABASE":MONGO_DATABASE,
            "MONGO_USERNAME":MONGO_USERNAME,
            "MONGO_PASSWORD":MONGO_PASSWORD,
            "MONGO_ADMIN_NODE":MONGO_ADMIN_NODE,
            "MONGO_SSL_CERT_PATH":MONGO_SSL_CERT_PATH
        })
        # Construct MongoDB URI with optional credentials and auth source
        credentials = f"{MONGO_USERNAME}:{MONGO_PASSWORD}@" if MONGO_USERNAME and MONGO_PASSWORD else ""
        auth_source = f"?authSource={MONGO_ADMIN_NODE}" if MONGO_ADMIN_NODE else ""
        # Construct the MongoDB URI
        if credentials or auth_source:
        # If either credentials or auth_source exists
            mongo_uri = f"mongodb://{credentials}{MONGO_HOST}:{MONGO_PORT}/{MONGO_DATABASE}{auth_source}"
        else:
        # If neither credentials nor auth_source exists
            mongo_uri = f"mongodb://{MONGO_HOST}:{MONGO_PORT}/{MONGO_DATABASE}"

        if MONGO_SSL_CERT_PATH.lower() in ('true', '1'):
            mongo_client = MongoClient(
                mongo_uri, 
                tls=True, 
                tlsCAFile=MONGO_SSL_CERT_PATH,  
                tlsAllowInvalidCertificates=False 
            )
        else:
            mongo_client = MongoClient(mongo_uri)
        return  mongo_client[MONGO_DATABASE]

    except Exception as e:
        print("Error connecting to MongoDB: ", e)
        return None
