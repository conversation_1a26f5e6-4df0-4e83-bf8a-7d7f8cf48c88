"""Created location column in users table

Revision ID: e020ae2ee5d4
Revises: 222fe934b10e
Create Date: 2025-04-11 13:16:07.258148

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e020ae2ee5d4'
down_revision: Union[str, None] = '222fe934b10e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('users', sa.Column('location', sa.Text(), nullable=True))


def downgrade() -> None:
    op.drop_column('users', 'location')
