from dotenv import load_dotenv
import os
from passlib.context import Crypt<PERSON>ontext
from fastapi import Request, HTTPException
from datetime import datetime
import logging
from datetime import datetime, timedelta
from hashids import Hashids

# Load environment variables from a .env file
load_dotenv()

# Access environment variables
UNIVERSAL_PASSWORD = os.getenv("UNIVERSAL_PASSWORD")
SECRET_KEY = os.getenv("SECRET_KEY")
ALGORITHM = os.getenv("ALGORITHM")
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", 30))
FORGET_PASSWORD_SECRET_KEY = os.getenv("FORGET_PASSWORD_SECRET_KEY")

DB_USER = os.getenv("DB_USER")
DB_PASSWORD = os.getenv("DB_PASSWORD")
DB_HOST = os.getenv("DB_HOST")
DB_PORT = os.getenv("DB_PORT")
DB_NAME = os.getenv("DB_NAME")
DB_DRIVER = os.getenv("DB_DRIVER")

OPENSEARCH_HOST = os.getenv("OPENSEARCH_HOST")
OPENSEARCH_PORT = os.getenv("OPENSEARCH_PORT")
OPENSEARCH_USER = os.getenv("OPENSEARCH_USER")
OPENSEARCH_PASSWORD = os.getenv("OPENSEARCH_PASSWORD")
OPENSEARCH_RESUME_INDEX = os.getenv("OPENSEARCH_RESUME_INDEX")
OPENSEARCH_REQUISITION_EMBEDDING_INDEX = os.getenv("OPENSEARCH_REQUISITION_EMBEDDING_INDEX")
OPENSEARCH_RC_INDEX = os.getenv("OPENSEARCH_RC_INDEX")
OPENSEARCH_CANDIDATE_EMBEDDING_INDEX = os.getenv("OPENSEARCH_CANDIDATE_EMBEDDING_INDEX")
OPENSEARCH_CPA_INDEX = os.getenv("OPENSEARCH_CPA_INDEX")
OPENSEARCH_RECRUITER_REQUISITION_INDEX = os.getenv("OPENSEARCH_RECRUITER_REQUISITION_INDEX")
SEARCH_CANDIDATES_SIMILARITY_THRESHOLD = os.getenv("SEARCH_CANDIDATES_SIMILARITY_THRESHOLD")

AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
AWS_DEFAULT_REGION = os.getenv("AWS_DEFAULT_REGION")
AWS_BUCKET = os.getenv("AWS_BUCKET")
AWS_USE_PATH_STYLE_ENDPOINT = os.getenv("AWS_USE_PATH_STYLE_ENDPOINT")
AWS_URL = os.getenv("AWS_URL")
AWS_RESUME_FOLDER = os.getenv("AWS_RESUME_FOLDER")
AWS_VIDEO_FOLDER = os.getenv("AWS_VIDEO_FOLDER")
AWS_PROFILE_FOLDER = os.getenv("AWS_PROFILE_FOLDER")
AWS_AUTOMATION_RESUME_FOLDER = os.getenv("AWS_AUTOMATION_RESUME_FOLDER")
AWS_ORGANIZATION_LOGO_FOLDER = os.getenv("AWS_ORGANIZATION_LOGO_FOLDER")

APP_HOST = os.getenv("APP_HOST")
FORGET_PASSWORD_URL = os.getenv("FORGET_PASSWORD_URL")
USER_INVITE_URL = os.getenv("USER_INVITE_URL")
INTERVIEW_PREP_URL = os.getenv("INTERVIEW_PREP_URL")

RECRUITER_APP_HOST = os.getenv("RECRUITER_APP_HOST")
CANDIDATE_THRESHOLD_SCORE = os.getenv("CANDIDATE_THRESHOLD_SCORE")
RESUME_THRESHOLD_SCORE = os.getenv("RESUME_THRESHOLD_SCORE")
RECOMMENDED_CANDIDATE_THRESHOLD_SCORE = os.getenv("RECOMMENDED_CANDIDATE_THRESHOLD_SCORE")

REQUISITION_SIMILARITY_THRESHOLD = float(os.getenv("REQUISITION_SIMILARITY_THRESHOLD"))

PWD_CONTEXT = CryptContext(schemes=["bcrypt"], deprecated="auto")

OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
LLM_BASE_URL = os.getenv("LLM_BASE_URL")
MAIL_CONFIG = {
    "MAIL_USERNAME": os.getenv("MAIL_USERNAME"),
    "MAIL_PASSWORD": os.getenv("MAIL_PASSWORD"),
    "MAIL_FROM": os.getenv("MAIL_FROM"),
    "MAIL_PORT": os.getenv("MAIL_PORT"),
    "MAIL_SERVER": os.getenv("MAIL_SERVER"),
    "MAIL_FROM_NAME": os.getenv("MAIL_FROM_NAME"),
    "SENDGRID_API_KEY": os.getenv("SENDGRID_API_KEY"),
    "CONTACT_PERSON": 'Zafar',
}
RECRUITER_OUTREACH_MAIL_FROM = os.getenv("RECRUITER_OUTREACH_MAIL_FROM")
RECRUITER_OUTREACH_REPLY_TO = os.getenv("RECRUITER_OUTREACH_REPLY_TO")
INTERVIEW_SENDER_EMAIL = os.getenv("INTERVIEW_SENDER_EMAIL")

INTERVIEW_DURATION = int(os.getenv("INTERVIEW_DURATION", 60))
PWD_CONTEXT = CryptContext(schemes=["bcrypt"], deprecated="auto")

JWT_TOKEN_EXPIRY_TIME = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)


EMBEDDING_MODEL_API = os.getenv("EMBEDDING_MODEL_API")
EMBEDDING_MODEL_API_KEY = os.getenv("EMBEDDING_MODEL_API_KEY")
EMBEDDING_MODEL_NAME = os.getenv("EMBEDDING_MODEL_NAME")


BROKER_HOST = os.getenv("BROKER_HOST")
BROKER_PORT = os.getenv("BROKER_PORT")
BROKER_USER = os.getenv("BROKER_USER")
BROKER_PASS = os.getenv("BROKER_PASS")
BROKER = os.getenv("BROKER")

CONTACT_US_EMAIL = os.getenv("CONTACT_US_EMAIL")

ENVIRONMENT = os.getenv("ENVIRONMENT")

MONGO_HOST = os.getenv('MONGO_HOST', 'localhost')  # Standardized environment variable prefix
MONGO_PORT = os.getenv('MONGO_PORT', '27017')
MONGO_DATABASE = os.getenv('MONGO_DATABASE', None)
MONGO_USERNAME = os.getenv('MONGO_USERNAME', None)
MONGO_PASSWORD = os.getenv('MONGO_PASSWORD', None)
MONGO_ADMIN_NODE = os.getenv('MONGO_ADMIN_NODE', None)
MONGO_SSL_CERT_PATH = os.getenv('MONGO_SSL_CERT_PATH', False)

SCRAPINGDOG_API_URL = os.getenv('SCRAPINGDOG_API_URL', '')
SCRAPINGDOG_API_KEY = os.getenv('SCRAPINGDOG_API_KEY', '')

HASHID_SALT = os.getenv("HASHID_SALT")
S3_URL = os.getenv("S3_URL")

AWS_REGION_BEDROCK = os.getenv("AWS_REGION_BEDROCK")
BEDROCK_MODEL_ID = os.getenv("BEDROCK_MODEL_ID")
BEDROCK_FUNC_CALLING_MODEL_ID = os.getenv("BEDROCK_FUNC_CALLING_MODEL_ID")

def getCurrentUser(request: Request):
    if hasattr(request.state, "user"):
        return request.state.user
    raise HTTPException(status_code=400, detail="User not found")


def returnResponse(
    success: bool, data: dict = {}, message: str = "", status_code: int = 200, errors: dict = {}
):
    if len(errors) > 0:
        return {
            "success": success,
            "data": data,
            "message": message,
            "status_code": status_code,
            "errors": errors,
        }
    else:
        return {
            "success": success,
            "data": data,
            "message": message,
            "status_code": status_code,
        }


# Configure logging with dynamic filename
log_directory = "storage/logs/"
current_date = datetime.now().strftime("%Y-%m-%d")
filename = f"system{current_date}.log"

os.makedirs(log_directory, exist_ok=True)

logging.basicConfig(
    filename=os.path.join(log_directory, filename),
    format="%(levelname)s %(asctime)s %(filename)s:%(lineno)d] %(message)s",
    datefmt="%d-%b-%y %H:%M:%S",
    level=logging.INFO,  # Set the default logging level
)

hash_id = Hashids(salt=HASHID_SALT, min_length=10)