exclude_user_prefixes = [
    "/api/v1/requisition/get-requisition/",
    "/api/v1/organization/get-organization/",
    "/api/v1/recruiter/",  # exclude recruiter routes
    "/api/v1/file-manager/get-interview-videos/",
    "/api/v1/get-user-interview-link/",
    "/api/v1/interview-email/",
    "/api/v1/requisition/create-evaluation-criteria/",
    "/api/v1/interview/check-ai-generated-content-percentage/",
    "/api/v1/populate-video-links",
    "/api/v1/out-reach/send-recruiter-email/",
    "/api/v1/out-reach/generate-recruiter-outreach-email/",
    "/api/v1/requisition/get-resume-no-auth/",
    "/api/v1/requisition/get-user-requisition-no-auth/",
    "/api/v1/admin/update-requisition",
    "/api/v1/dropdown/",
    "/api/v1/insert_transcript",
    "/api/v1/update_transcript",
    "/api/v1/map_requistion",
    "/api/v1/update_recruiter_info",
]

exclude_common_routes = [
    "/api/v1/login",
    "/api/v1/similarity/",
    "/api/v1/register-guest-user",
    "/api/v1/create-recruiter-access-token",
    "/api/v1/send-interview-prepare-link",
    "/api/v1/migration/convert",
    "/api/v1/migration/insert",
    "/api/v1/migration/bulk-convert-insert",
    "/api/v1/migration/bulk-convert-update",
    "/api/v1/migration/bulk-convert-insert-users-table",
    "/api/v1/enrich-user-company-info-bulk",
    "/api/v1/fix-user-company-details-bulk",
    "/api/v1/migration/bulk-insert-users",
    "/api/v1/search-candidate/determine-context",
    "/api/v1/validate-user-access-token",
    "/api/v1/validate-recruiter",
    "/api/v1/register",
    "/api/v1/register-recruiter",
    "/api/v1/login-recruiter",
    "/api/v1/forget-password",
    "/api/v1/reset-password",
    "/api/v1/requisition/requisitions",
    "/api/v1/requisition/scrape",
    "/api/v1/extract-skills",
    "/docs",
    "/openapi.json",
    "/api/v1/requisition/parse-requisition",
    "/api/v1/get-skills",
    "/api/v1/automate-user-profile",
    "/api/v1/JD-matching",
    "/api/v1/update-automate-user-profile",
    "/api/v1/interview-email/get-missed-interview-users",
    "/api/v1/interview-email/get-half-hour-interview-users",
    "/api/v1/interview-email/get-today-interview-users",
    "/api/v1/register-revamp",
    "/api/v1/cities",
    "/api/v1/profession/get-professions",
    "/api/v1/countries",
    "/api/v1/testing",
    "/api/v1/add-currency",
    "/api/v1/job-dump-test",
    "/api/v1/job-onboarding",
    "/api/v1/populate-requisition-embeddings",
    "/api/v1/activity/store-guest-recruiter-cta",
    "/api/v1/send-test-email",
    "/api/v1/out-reach/create-recruiter-outreach-for-clay",
    "/api/v1/out-reach/resend-initiated-email",
    "/api/v1/get_recommended_candidates",
    "/api/v1/search-candidates",
    "/api/v1/get-matched-filters",
    "/api/v1/remove-duplicate-cities",
    "/api/v1/requisition/store-question-feedback-no-auth",
    "/api/v1/requisition/update-interview-state-machine-nodes-no-auth",
    "/api/v1/qc-automation",
    "/api/v1/recruiter-reset-password",
    "/api/v1/recruiter-forget-password"
]
# exclude_user_routes = exclude_routes_for_all

exclude_recruiter_prefixes = [
    "/api/v1/user/",
    "/api/v1/interview/",
    "/api/v1/similarity/",
    "/api/v1/file-manager/",
    "/api/v1/organization/",
    "/api/v1/requisition/get-requisition/",
    "/api/v1/organization/get-organization/",
    "/api/v1/file-manager/get-interview-videos/",
    "/api/v1/get-user-interview-link/",
    "/api/v1/activity/",
    "/api/v1/requisition/pre-evaluate-job-requisition/",
    "/api/v1/requisition/create-recommendations/",
    "/api/v1/requisition/pre-evaluate/",
    "/api/v1/requisition/get-recommendations/",
    "/api/v1/requisition/create-evaluation-criteria/",
    "/api/v1/interview/check-ai-generated-content-percentage/",
    "/api/v1/interview-email/",
    "/api/v1/dispatch/",
    "/api/v1/populate-video-links",
    "/api/v1/out-reach/send-recruiter-email/",
    "/api/v1/out-reach/generate-recruiter-outreach-email/",
    "/api/v1/recruiter/candidate/get-public-candidate/",
    "/api/v1/recruiter/candidate/create-public-candidate-link/",
    "/api/v1/interview-prep/",
    "/api/v1/cpa/",
    "/api/v1/requisition/get-resume-no-auth/",
    "/api/v1/requisition/get-user-requisition-no-auth/",
    "/api/v1/admin/update-requisition",
    "/api/v1/dropdown/",
    "/api/v1/recruiter/candidate/get-public-non-interviewed-candidates/",
]
# exclude_recruiter_routes = exclude_routes_for_all
