import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from fastapi.middleware.cors import CORSMiddleware
from routers import routers
from middleware.authenticate import AuthMiddleware
from middleware.authenticate_recruiter import <PERSON><PERSON><PERSON>erAuthMiddleware
from starlette.middleware import Middleware
from typing import List, Dict
from fastapi.responses import PlainTextResponse
import ast
from http_custom_handler import register_exception_handlers


middleware = [Middleware(AuthMiddleware), Middleware(RecruiterAuthMiddleware)]

origins = [
    "http://localhost:3000",
    "http://************",
    "http://demo.snabup.com",
    "https://staging.vettio.com",
    "https://vettio.com",
    "http://localhost:3001",
    "https://vettio.netlify.app"
    "https://recruiter.vettio.com",
    "https://recruiter-staging.vettio.com",
    "https://development.d2uwyo8gwoyz36.amplifyapp.com",
    "https://statging-app.vettio.com"
]

app = FastAPI(middleware=middleware)

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,  # List of allowed origins
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)


for router in routers:
    app.include_router(router, prefix="/api/v1")


@app.get("/")
async def root():
    return {"message": "Welcome to FastAPI"}


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)

register_exception_handlers(app)
