DB_NAME=
DB_USER=
DB_PASSWORD=
DB_HOST=
DB_PORT=
DB_DRIVER=
UNIVERSAL_PASSWORD=

OPENSEARCH_SCHEME=
OPENSEARCH_HOST=
OPENSEARCH_PORT=
OPENSEARCH_USER=
OPEN<PERSON>ARCH_PASSWORD=
OP<PERSON><PERSON>ARCH_RESUME_INDEX=
SEARCH_CANDIDATE_SIMILARITY_THRESHOLD=

SECRET_KEY=
ALGORITHM=
ACCESS_TOKEN_EXPIRE_MINUTES=
FORGET_PASSWORD_SECRET_KEY=

MAIL_SERVER=
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_FROM=
MAIL_PORT=
MAIL_FROM_NAME=

OPENAI_API_KEY=
LLM_BASE_URL=

BROKER=
BROKER_HOST=
BROKER_PORT=

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=
AWS_BUCKET=
AWS_URL=
AWS_RESUME_FOLDER=

APP_HOST=
FORGET_PASSWORD_URL=
USER_INVITE_URL=
INTERVIEW_PREP_URL=

RECRUITER_APP_HOST=
CANDIDATE_THRESHOLD_SCORE=
RESUME_THRESHOLD_SCORE=

SENDGRID_API_KEY=
LLM_BASE_URL=

CONTACT_US_EMAIL=

MONGO_HOST=
MONGO_DATABASE=
MONGO_PORT=
MONGO_ADMIN_NODE=
MONGO_USERNAME=
MONGO_PASSWORD=
MONGO_SSL_CERT_PATH=

SCRAPINGDOG_API_URL=
SCRAPINGDOG_API_KEY=

HASHID_SALT=
AWS_REGION_BEDROCK=
BEDROCK_MODEL_ID=
BEDROCK_FUNC_CALLING_MODEL_ID=

OPENSEARCH_RECRUITER_REQUISITION_INDEX=
