from pydantic import BaseModel
import random
import string
from .common_imports import *
from fastapi.exceptions import RequestValidationError
from schemas.user import User<PERSON>ase<PERSON>anda<PERSON>, User, PushToTalkUserCreate, UserCreateRevamp, GuestUserCreate
from database import get_mongodb_client, get_db
from schemas.auth import (
    ForgetPasswordRequest,
    ResetPasswordRequest,
    ForgetPasswordResponse,
    ValidateRecruiter,
    AccessTokenRequest,
    EmailRequest
)
from services.user.user import UserServiceClass
from services.auth import AuthServiceClass
from schemas.recruiter import RecruiterResponse, RecruiterCreate
from dependencies import RECRUITER_APP_HOST
from services.notification.email import send_email_background
from celery_tasks.test_tasks import my_test_task

router = APIRouter(
    tags=["Authentication"],
)

auth_service_object = AuthServiceClass()


@router.post("/login", response_model=ApiSuccessResponse[User])
def login(
    request: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)
):
    try:
        user = auth_service_object.login_user(request, db)
        return ApiSuccessResponse(message="User login successfully", data=user)

    except AuthFailedException as e:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post("/push-to-talk-register-user", response_model=ApiSuccessResponse[User])
def register(request: PushToTalkUserCreate, db: Session = Depends(get_db)):
    try:
        user_service_object = UserServiceClass()
        user = user_service_object.register_user(request, db)
        return ApiSuccessResponse(message="User registered successfully", data=user)

    except EntityAlreadyExistsException as e:
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.post("/register-guest-user")
def register_guest(request: GuestUserCreate, db: Session = Depends(get_db), mongodb_db: Session = Depends(get_mongodb_client)):
   try:


       user = UserServiceClass().register_guest_user(request, db, mongodb_db)
       return ApiSuccessResponse(message="User registered successfully", data=user)
   except (EntityAlreadyExistsException, ValueError) as e:
       raise HTTPException(status_code=400 if isinstance(e, ValueError) else 409, detail=str(e))
   except Exception:
       raise HTTPException(status_code=500, detail="Internal server error.")


@router.post("/register-revamp", response_model=ApiSuccessResponse[User])
def register(request: UserCreateRevamp, db: Session = Depends(get_db)):
    try:
        user_service_object = UserServiceClass()
        user = user_service_object.register_user_revamp(request, db)
        return ApiSuccessResponse(message="User registered successfully", data=user)

    except EntityAlreadyExistsException as e:
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.post(
    "/forget-password", response_model=ApiSuccessResponse[ForgetPasswordResponse]
)
def forget_password(
    forget_password_request: ForgetPasswordRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
):
    try:
        response = auth_service_object.forget_password(
            forget_password_request.email, background_tasks, db
        )

        return ApiSuccessResponse(
            message="Reset link sent to your email", data=response
        )
    except EntityNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post("/reset-password", response_model=ApiSuccessResponse[User])
def reset_password(
    reset_password_request: ResetPasswordRequest, db: Session = Depends(get_db)
):
    try:
        user = auth_service_object.reset_password(reset_password_request, db)
        return ApiSuccessResponse(message="Password reset successfully", data=user)

    except EntityNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post("/create-recruiter-access-token")
def create_recruiter_access_token(
    input_data: ForgetPasswordRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
):
    try:

        recruiter = auth_service_object.create_recruiter_access_token(input_data.email, db)
        redirect_link = f"{RECRUITER_APP_HOST}?token={recruiter.token}"
        # email_body = {
        #     "name": f"{recruiter.first_name} {recruiter.last_name}",
        #     "redirect_link": redirect_link,
        # }
        # send_email_background(
        #     background_tasks,
        #     "Shortlist your candidate",
        #     recruiter.email,
        #     email_body,
        #     "invite-user.html",
        # )
        return ApiSuccessResponse(
            message="Recruiter access token created successfully", data=redirect_link
        )
    except EntityNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post("/validate-recruiter", response_model=ApiSuccessResponse[RecruiterResponse])
def validate_recruiter(
    validation_data: ValidateRecruiter, db: Session = Depends(get_db)
):
    try:
        response = auth_service_object.validate_recruiter(validation_data, db)
        return ApiSuccessResponse(message="Recruiter validated successfully", data=response)
    except EntityNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.post("/validate-user-access-token", response_model=ApiSuccessResponse[User])
def validate_user_access_token(
    access_token_request: AccessTokenRequest, db: Session = Depends(get_db)
):
    try:
        user = auth_service_object.validate_user_access_token(access_token_request, db)
        return ApiSuccessResponse(message="User validated successfully", data=user)
    except EntityNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.post("/register-recruiter", response_model=ApiSuccessResponse[RecruiterResponse])
def register_recruiter(request: RecruiterCreate, db: Session = Depends(get_db)):
    try:
        user_service_object = UserServiceClass()
        user = user_service_object.register_recruiter_user(request, db)
        return ApiSuccessResponse(message="Recruiter registered successfully", data=user)

    except EntityAlreadyExistsException as e:
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.post("/login-recruiter", response_model=ApiSuccessResponse[RecruiterResponse])
def login_recruiter(
    request: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)
):
    try:
        user = auth_service_object.login_recruiter(request, db)
        return ApiSuccessResponse(message="User login successfully", data=user) #need to confirm its logic, whether it should be implemented same?
    except AuthFailedException as e:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/get-user-interview-link/{email}")
def create_user_interview_link(
    email: str, db: Session = Depends(get_db)
):
    try:
        response = auth_service_object.create_user_interview_link(email, db)
        return ApiSuccessResponse(message="Interview link created successfully", data=response)
    except EntityNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post("/send-interview-prepare-link")
def send_interview_prepare_link(
    payload: EmailRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
):
    try:
        response = auth_service_object.send_interview_prepare_link(
            payload.email, background_tasks, db
        )
        return ApiSuccessResponse(message="Interview link sent successfully", data=response)
    except EntityNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.post(
    "/recruiter-forget-password", response_model=ApiSuccessResponse[ForgetPasswordResponse]
)
def recruiter_forget_password(
    forget_password_request: ForgetPasswordRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
):
    try:
        response = auth_service_object.recruiter_forget_password(
            forget_password_request.email, background_tasks, db
        )

        return ApiSuccessResponse(
            message="Reset link sent to your email", data=response
        )
    except EntityNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post("/recruiter-reset-password", response_model=ApiSuccessResponse[RecruiterResponse])
def recruiter_reset_password(
    reset_password_request: ResetPasswordRequest, db: Session = Depends(get_db)
):
    try:
        user = auth_service_object.recruiter_reset_password(reset_password_request, db)
        return ApiSuccessResponse(message="Password reset successfully", data=user)

    except EntityNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


