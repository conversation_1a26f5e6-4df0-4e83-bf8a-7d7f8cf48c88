import uuid
from fastapi import APIRouter, Depends, HTTPException, Request, status, Query
from services.jd_tool import JDService
from database import get_mongodb_client
from .common_imports import *
from services.interview.interview import InterviewServiceClass
from services.interview.Intro_video import IntroVideoServiceClass
from schemas.question_feedback import Question<PERSON><PERSON><PERSON>B<PERSON>, QuestionFeedback
from schemas.interview import Interview, UpdateInterviewStatus, UserId, UpdateInterviewStateMachineNode
from services.interview.candidate_picture import VideoProcessor
from services.external.openai import OpenAIServiceClass
from models.interview_feedback import InterviewFeedback
import json
from fastapi.encoders import jsonable_encoder
import logging
from services.interview_prep import InterviewPrepService
from database import get_mongodb_client
from schemas.interview_prep import InterviewPrepForm, Data, Categories, UserData, QuestionData
from database import get_mongodb_client
from services.filemanager.filemanager import FileManagerClass
from services.parser.resume_parser import ResumeParserService
from services.external.openai import OpenAIServiceClass
from services.parser.text_extractor_service import TextExtractorService
from dependencies import (
    getCurrentUser,
    returnResponse,
    OPENSEARCH_RESUME_INDEX,
    AWS_RESUME_FOLDER,
)
from typing import List
from pydantic import BaseModel, Field
from services.user.user import UserServiceClass

user_service_object = UserServiceClass()

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/job-description", tags=["Job Description"]
)

jd_service_object = JDService()

from pydantic import BaseModel, Field

class UserData(BaseModel):
    answer: str = ""
    question: str = ""

class jdData(BaseModel):
    about_company : str = " "
    role_title : str = " "
    experience : str = " "
    location : str = " "
    salary_range : str = " "
    stakeholders : str = " "


from typing import Dict
class updateJDData(BaseModel):
    obj_id: str = Field(description="object id")
    job_description: str = Field(description="Job Description")


@router.post("/guardrail")
def check_guardrail(
    request: Request,
    userData: UserData,
):
    """
    Endpoint to generate and store interview preparation categories.
    """
    try:

        response = jd_service_object.guardrail(
            request, userData
        )

        return ApiSuccessResponse(message="Guardrail implemented", data=jsonable_encoder(response))
    
    except HTTPException as http_ex:
        # Re-raise HTTPException directly to avoid duplication
        raise http_ex

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

# @router.post("/generate-jd")
# def generate_kpis(request: Request, data: jdData, sql_db: Session = Depends(get_db), mongodb_db: Session = Depends(get_mongodb_client)):
# #     mongodb_db: Session = Depends(get_mongodb_client),):
#     try:
#         response = jd_service_object.generate_kpis_and_JD(request, data, sql_db, mongodb_db)
        
#         return ApiSuccessResponse(message="KPIs generated and stored successfully", data=jsonable_encoder(response))

#     except Exception as e:
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
#         )

@router.post("/generate-kpis")
def generate_kpis(
    request: Request, 
    data: jdData, 
    sql_db: Session = Depends(get_db), 
    mongodb_db: Session = Depends(get_mongodb_client)
):
    try:
        response = jd_service_object.generate_kpis(request, data, sql_db, mongodb_db)
        return ApiSuccessResponse(message="KPIs generated and stored successfully", data=jsonable_encoder(response))
    
    except HTTPException as http_ex:
        # Re-raise HTTPException directly to avoid duplication
        raise http_ex
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get("/get-jds")
def generate_kpis(
    request: Request, 
    sql_db: Session = Depends(get_db), 
    mongodb_db: Session = Depends(get_mongodb_client)
):
    try:
        response = jd_service_object.get_jds(request, sql_db, mongodb_db)
        return ApiSuccessResponse(message="JDs retrieved successfully", data=jsonable_encoder(response))
    
    except HTTPException as http_ex:
        # Re-raise HTTPException directly to avoid duplication
        raise http_ex
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
class JobDescriptionRequest(BaseModel):
    document_id: str
    selected_kpis: List[str]
    
@router.post("/generate-job-description")
def generate_job_description(
    request: Request, 
    data: JobDescriptionRequest, 
    mongodb_db: Session = Depends(get_mongodb_client)
):
    try:
        response = jd_service_object.generate_job_description(request, data.document_id, data.selected_kpis, mongodb_db)
        return ApiSuccessResponse(message="Job description generated and updated successfully", data=jsonable_encoder(response))
    
    except HTTPException as http_ex:
        # Re-raise HTTPException directly to avoid duplication
        raise http_ex
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post("/update-job-description")
def update_job_description(request: Request, data: updateJDData, sql_db: Session = Depends(get_db), mongodb_db: Session = Depends(get_mongodb_client)):
    try:
        response = jd_service_object.update_job_description(request, data, mongodb_db)
        
        return ApiSuccessResponse(message="Job description updated successfully", data=jsonable_encoder(response))
    
    except HTTPException as http_ex:
        # Re-raise HTTPException directly to avoid duplication
        raise http_ex

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

 
@router.get("/get-original-jd")
def get_og_job_description(
    request: Request,    
    document_id: str,
    mongodb_db: Session = Depends(get_mongodb_client)
):
    """
    API endpoint to fetch a specific job description for a user.
    """
    try:
        # Call the service method
        job_description = jd_service_object.get_original_description(request.state.user.id, document_id, mongodb_db)

        return {
            "message": "Job description retrieved successfully.",
            "data": job_description
        }

    except HTTPException as http_ex:
        # Re-raise HTTPException directly to avoid duplication
        raise http_ex
    except Exception as e:
        # Handle unexpected errors with a 500 status
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

 
@router.get("/get-updated-jd")
def get_og_job_description(
    request: Request,    
    document_id: str,
    job_description_id: str,
    mongodb_db: Session = Depends(get_mongodb_client)
):
    """
    API endpoint to fetch a specific job description for a user.
    """
    try:
        # Call the service method
        job_description = jd_service_object.get_updated_description(request.state.user.id, document_id, job_description_id, mongodb_db)

        return {
            "message": "Job description retrieved successfully.",
            "data": job_description
        }

    except HTTPException as http_ex:
        # Re-raise HTTPException directly to avoid duplication
        raise http_ex
    except Exception as e:
        # Handle unexpected errors with a 500 status
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )    



@router.get("/get-job-description")
def get_job_description(
    user_id: int,
    document_id: str,
    job_description_id: str,
    mongodb_db: Session = Depends(get_mongodb_client)
):
    """
    API endpoint to fetch a specific job description for a user.
    """
    try:
        # Call the service method
        job_description = jd_service_object.get_job_description(user_id, document_id, job_description_id, mongodb_db)

        return {
            "message": "Job description retrieved successfully.",
            "data": job_description
        }

    except HTTPException as http_ex:
        # Re-raise HTTPException directly to avoid duplication
        raise http_ex
    except Exception as e:
        # Handle unexpected errors with a 500 status
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.get("/get-static")
def get_static(
    request:Request,
):
    try:
        # Call the service method
        job_description = jd_service_object.get_text_and_audio(request)

        return {
            "message": "Job description retrieved successfully.",
            "data": job_description
        }

    except ValueError as ve:
        # Handle known exceptions with a 404 status
        raise HTTPException(status_code=404, detail=str(ve))
    except Exception as e:
        # Handle unexpected errors with a 500 status
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )