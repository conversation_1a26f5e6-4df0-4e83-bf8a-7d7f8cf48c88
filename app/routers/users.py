from celery import chain 
from .common_imports import *
from typing import List
from sqlalchemy.orm import Session
from schemas.user import (
    User<PERSON>pdate,
    DeleteUser,
    User,
    UserDetail,
    CompleteUserSignup,
    UpdateUserPreferences,
    InterviewPrepForm
)
from celery_tasks.upload_resume import upload
from schemas.user_education import UserEducation, UserEducationCreateUpdate
from schemas.user_experience import UserExperience, UserExperienceCreateUpdate
from schemas.application import ApplicationResponseSchema
from schemas.interview import (
    InterviewBase,
    Interview,
    InterviewAndReschduleInterviews,
)
from schemas.requisition import RequsitionsResponseSchema, RequisitionSchema
from schemas.opensearch import ParsedResume
from schemas.user_meta import UserMetaCreate
from schemas.user_requisition import UserRequisitionCreate
from services.notification.email import send_email_background
from services.filemanager.filemanager import FileManagerClass
from services.parser.resume_parser import ResumeParserService
from services.external.openai import OpenAIServiceClass
from services.external.opensearch import OpenSearchServiceClass
from services.user.user import UserServiceClass
from services.user.user_interview import UserInterviewServiceClass
from services.requisition.requisition import RequisitionServiceClass
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from helper.helper import (
    get_user_education_object,
    get_user_experience_object,
    convert_utc_to_local,
)
from dependencies import (
    getCurrentUser,
    returnResponse,
    OPENSEARCH_RESUME_INDEX,
    AWS_RESUME_FOLDER,
    AWS_PROFILE_FOLDER,
)
from database import get_db, get_mongodb_client
from services.parser.text_extractor_service import TextExtractorService
from services.user.user_requisition import UserRequisitionServiceClass
from schemas.user_requisition import UserRequisitionResponse
from datetime import datetime
from services.auth import AuthServiceClass
from services.interview.pre_evaluate import PreEvaluate
from dependencies import MAIL_CONFIG
import logging
from schemas.interview_user_agent import (
    InterviewUserAgentStoreRequest,
    InterviewUserAgent,
)
from services.scrapper.scrapping_dog import ScrappingDogServiceClass
from celery_tasks.enrich_company_info import enrich_company_info
logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/user", tags=["Users"], dependencies=[Depends(getCurrentUser)]
)

user_service_object = UserServiceClass()
user_interview_service_object = UserInterviewServiceClass()
user_requisition_service_object = UserRequisitionServiceClass()
auth_service_object = AuthServiceClass()
scrapingdog_service_object = ScrappingDogServiceClass()

logger = logging.getLogger(__name__)


@router.get("/profile", response_model=ApiSuccessResponse[User])
def get_user(
    request: Request,
    db: Session = Depends(get_db),
):
    try:
        user = user_service_object.get_user(request, db)
        return ApiSuccessResponse(message="User registered successfully", data=user)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post("/update-user", response_model=ApiSuccessResponse[User])
def update_user(
    user_data: UserUpdate,
    request: Request,
    db: Session = Depends(get_db),
):
    try:
        user = user_service_object.update_user(user_data, request, db)
        return ApiSuccessResponse(message="User updated successfully", data=user)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
        # have to fix this method later with return httpcode and exception work


@router.post("/complete-signup", response_model=ApiSuccessResponse[User])
def update_user(
  request: Request,
  background_tasks: BackgroundTasks,
  form_data: CompleteUserSignup = Depends(CompleteUserSignup.as_form),
  file: UploadFile = File(...),
  db: Session = Depends(get_db),
  mongodb: Session = Depends(get_mongodb_client)
):
    try:
        resume_parser_object = ResumeParserService()
        user = user_service_object.complete_user_signup(form_data, request, db)


        if file is not None:
           text_extractor_service = TextExtractorService()
           file_manager_object = FileManagerClass()

           if not text_extractor_service.is_supported_extension(file.filename):
               raise e
        #    temp_file_path = file_manager_object.save_temp_file(file)
        #    file_path = temp_file_path.get("filePath")

        #    file.file.seek(0)
        #    Save the file to s3 bucket
           file_name_object = FileManagerClass.save_file(
               file_manager_object,
               request,
               file,
               AWS_RESUME_FOLDER + str(user.id) + "/",
               "resume",
           )
           file_path = file_name_object['file_path']
           logger.info("Sending resume to celery...")

        #    upload.delay(request.state.user.id, file_path, file_name_object)
            #  enrich_company_info.delay(request.state.user.id)

           chain(
               
                upload.s(request.state.user.id, file_path, file_name_object),
                enrich_company_info.si(request.state.user.id)
           ).apply_async()
           
        return ApiSuccessResponse(message="User updated successfully", data=user)
    except Exception as e:
      raise HTTPException(
          status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
      )

@router.post("/update-profile-picture", response_model=ApiSuccessResponse[User])
def update_user_profile_picture(
    request: Request,
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
):
    try:
        if file is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="File not found"
            )
        if file.content_type not in ["image/jpeg", "image/jpg", "image/png"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Unsupported file type allowed only jpeg, jpg, png",
            )
        # Save the file to s3 bucket
        file_name_object = FileManagerClass().save_file(
            request,
            file,
            AWS_PROFILE_FOLDER + str(request.state.user.id) + "/",
            "profile_picture",
        )
        user = user_service_object.update_user_profile_picture(
            file_name_object, request, db
        )
        return ApiSuccessResponse(
            message="Profile picture updated successfully", data=user
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post("/update-user-preferences", response_model=ApiSuccessResponse[User])
def update_user_preferences(
    request: Request,
    payload: UpdateUserPreferences,
    db: Session = Depends(get_db),
):
    try:
        user = user_service_object.update_user_preferences(payload, request, db)
        return ApiSuccessResponse(message="User updated successfully", data=user)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post(
    "/update-educations", response_model=ApiSuccessResponse[List[UserEducation]]
)
def update_education(
    user_educations: List[UserEducationCreateUpdate],
    request: Request,
    db: Session = Depends(get_db),
):
    try:
        user_education_object = user_service_object.update_education(
            user_educations, request, db
        )
        return ApiSuccessResponse(
            message="User updated successfully", data=user_education_object
        )

    except EntityNotFoundException as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail=str(e))

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.delete('/delete-education/{id}')
def delete_educations(
    request: Request,
    id: int,
    db: Session = Depends(get_db),
):
    try:
        user_education_object = user_service_object.delete_education(request, id, db)
        return ApiSuccessResponse(
            message="User education deleted successfully", data=user_education_object
        )
    except EntityNotFoundException as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail=str(e))

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.post(
    "/update-experience", response_model=ApiSuccessResponse[List[UserExperience]]
)
def update_experience(
    user_experiences: List[UserExperienceCreateUpdate],
    request: Request,
    db: Session = Depends(get_db),
):
    try:
        user_experience_object = user_service_object.update_experience(
            user_experiences, request, db
        )
        return ApiSuccessResponse(
            message="User updated successfully", data=user_experience_object
        )

    except EntityNotFoundException as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail=str(e))

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.delete('/delete-experience/{id}')
def delete_educations(
    request: Request,
    id: int,
    db: Session = Depends(get_db),
):
    try:
        user_education_object = user_service_object.delete_experience(request, id, db)
        return ApiSuccessResponse(
            message="User experience deleted successfully", data=user_education_object
        )
    except EntityNotFoundException as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail=str(e))

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.post(
    "/add-education", response_model=ApiSuccessResponse[UserEducation]
)
def add_education(
    user_education: UserEducationCreateUpdate,
    request: Request,
    db: Session = Depends(get_db),
):
    try:
        user_education_object = user_service_object.add_education(request,user_education, db)
        return ApiSuccessResponse(
            message="User Education added successfully", data=user_education_object
        )

    except EntityNotFoundException as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail=str(e))

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.post(
    "/add-experience", response_model=ApiSuccessResponse[UserEducation]
)
def add_education(
    user_experience: UserExperienceCreateUpdate,
    request: Request,
    db: Session = Depends(get_db),
):
    try:
        user_experience_object = user_service_object.add_experience(request,user_experience, db)
        return ApiSuccessResponse(
            message="User Experience added successfully", data=user_experience_object
        )

    except EntityNotFoundException as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail=str(e))

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )



@router.post("/schedule-interview", response_model=ApiSuccessResponse[Interview])
def schedule_user_interview(
    user_interview: InterviewBase,
    background_tasks: BackgroundTasks,
    request: Request,
    db: Session = Depends(get_db),
):
    try:
        interview_object = user_interview_service_object.schedule_user_interview(
            user_interview, request, db, background_tasks
        )
        return ApiSuccessResponse(
            message="Interview scheduled successfully", data=interview_object
        )

    except EntityAlreadyExistsException as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT, detail=str(e))

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post("/reschedule-interview/{id}", response_model=ApiSuccessResponse[Interview])
def schedule_user_interview(
    id: int,
    user_interview: InterviewBase,
    background_tasks: BackgroundTasks,
    request: Request,
    db: Session = Depends(get_db),
):
    try:
        interview_object = user_interview_service_object.reschedule_user_interview(
            id, user_interview, request, db, background_tasks
        )

        return ApiSuccessResponse(
            message="Interview rescheduled successfully", data=interview_object
        )
    except EntityNotFoundException as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail=str(e))

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get(
    "/get-interviews",
    response_model=ApiSuccessResponse[InterviewAndReschduleInterviews],
)
def get_user_interviews(
    request: Request,
    db: Session = Depends(get_db),
):
    try:
        interview_object = user_interview_service_object.get_user_interviews(
            request, db
        )
        return ApiSuccessResponse(message="Success", data=interview_object)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )



@router.get("/get-resume")
async def get_resume(request: Request, db: Session = Depends(get_db)):
    try:
        user = user_service_object.get_user_detail(request.state.user.id, db)
        user_recent_interview = user_interview_service_object.get_recent_user_interview(
            request, db
        )
        user["recent_interview_status"] = (
            user_recent_interview["status"] if user_recent_interview else None
        )
        return ApiSuccessResponse(message="Success", data=user)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


# This API is for internal use
# No need to change response of this API
@router.post("/delete-user")
async def delete_user(
    delete_user: DeleteUser, request: Request, db: Session = Depends(get_db)
):
    try:
        response = user_service_object.delete_user(
            delete_user.email, request, db)
        return returnResponse(True, response, "User deleted successfully")
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post(
    "/save-requisition/{id}", response_model=ApiSuccessResponse[UserRequisitionCreate]
)
async def save_requisition(id: int, request: Request, db: Session = Depends(get_db)):
    try:
        response = user_requisition_service_object.take_action_on_requisition(
            "save", id, request, db
        )
        return ApiSuccessResponse(
            message="Requisition saved successfully", data=response
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post(
    "/apply-requisition/{id}", response_model=ApiSuccessResponse[UserRequisitionCreate]
)
async def apply_requisition(
    id: int,
    background_tasks: BackgroundTasks,
    request: Request,
    db: Session = Depends(get_db),
):
    try:
        response = user_requisition_service_object.take_action_on_requisition(
            "apply", id, request, db
        )

        background_tasks.add_task(
            PreEvaluate().evaluate_candidate, response.requisition_id, request, db
        )

        will_email_send_to_user = (
            user_requisition_service_object.will_email_send_to_user(
                request, db)
        )
        if will_email_send_to_user is True:
            # Get Organization Name
            requisition = RequisitionServiceClass().get_requisition(
                request, response.requisition_id, db
            )
            # Create access token to verify user
            secret_token = auth_service_object.create_access_token(
                request.state.user.email
            )
            # Email Body
            redirect_link = f"{APP_HOST}{USER_INVITE_URL}?access_token={secret_token}&redirect_to=schedule-interview"
            print(redirect_link)
            email_body = {
                "name": f"{request.state.user.first_name} {request.state.user.last_name}",
                "redirect_link": redirect_link,
                "requisition_title": requisition["title"],
                "organization_name": requisition["organization"]["name"],
                "contact_email": MAIL_CONFIG["MAIL_FROM"],
                "app_name": MAIL_CONFIG["MAIL_FROM_NAME"],
            }
            print(email_body)
            # send email
            send_email_background(
                background_tasks,
                "Next Step: Schedule Your Interview!",
                request.state.user.email,
                email_body,
                "apply_to_requsition.html",
            )
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get("/requisitions", response_model=ApiSuccessResponse[UserRequisitionCreate])
async def get_user_requisitions(request: Request, db: Session = Depends(get_db)):
    try:
        response = user_requisition_service_object.get_user_requisitions(
            request, db)
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get(
    "/get-user-requisition",
    response_model=ApiSuccessResponse[UserRequisitionResponse]
)
def get_user_requisition(
    request: Request, background_tasks: BackgroundTasks, db: Session = Depends(get_db)
):
    try:
        response = user_requisition_service_object.get_user_requisition(
            request, db, background_tasks
        )
        return ApiSuccessResponse(
            message="Requisitions fetch successfully", data=response
        )
    except EntityNotFoundException as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.post("/save-user-meta")
async def save_user_meta(
    request: Request, payload: UserMetaCreate, db: Session = Depends(get_db)
):
    try:
        response = user_service_object.save_user_meta(request, payload, db)
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.post('/create-skills')
async def create_user_skills(
    request: Request,
    skills: List[str],
    db: Session = Depends(get_db)
):
    try:
        response = user_service_object.create_update_user_skill(skills, request, db)
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
    
@router.delete('/delete-skill/{id}')
async def delete_user_skills(
    request: Request,
    id: int,
    db: Session = Depends(get_db)
):
    try:
        response = user_service_object.delete_user_skill(request, id, db)
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
    
     

@router.get(
    "/scrape-user-company-info"
)
def scrape(
    request: Request,
    db: Session = Depends(get_db),
):
    try:
        response = scrapingdog_service_object.get_linkedin_profile_by_user_id(
            request.state.user.id, db
        )
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.post("/send-interview-with-sally")
def send_interview_email_with_sally(request: Request, background_tasks: BackgroundTasks):
    try:
       response = UserInterviewServiceClass().schedule_interview_with_sally(request, background_tasks, 'interview' )
       return ApiSuccessResponse(
            message="Interview link sent to your email", data=response
        )        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
    
@router.post("/send-upload-resume-email")
def send_interview_email_with_sally(request: Request, background_tasks: BackgroundTasks):
    try:
       response = UserInterviewServiceClass().schedule_interview_with_sally(request, background_tasks, 'resume')
       return ApiSuccessResponse(
            message="Interview link sent to your email", data=response
        )        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )