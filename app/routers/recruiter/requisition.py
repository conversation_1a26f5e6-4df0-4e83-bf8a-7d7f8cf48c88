from fastapi import APIRouter, Depends, status, HTTPException, Request
from database import get_db
from sqlalchemy.orm import Session
from typing import List
from services.requisition.requisition import RequisitionServiceClass
from ..common_imports import *
from helper.helper import get_authenticate_user
from schemas.requisition import (
    RequsitionsResponseSchema,
    RequisitionCreateSchema,
    RequisitionStatusUpdateSchema,
    RequisitionListingPayloadSchema,
    RequisitionsInterviewResponse,
    RequisitionInterviewListPayloadSchema,
)
from services.requisition.requisition import RequisitionServiceClass
from celery import chain
from schemas.organization_detail import OrganizationDetailsCreateSchema, OrganizationDetailSchema
from services.organization.organization_detail import OrganizationDetailsServiceClass
from celery_tasks.onboarding_tasks import send_cold_email_to_recruiter
from celery_tasks.post_requisition_process_tasks import (
    create_evaluation_criteria,
    create_recommended_candidates_from_similar_requisitions,
    add_llm_requisition_detail,
    create_requisition_vector_embeddings,
)

requsition_service = RequisitionServiceClass()
organization_detail_service = OrganizationDetailsServiceClass()

router = APIRouter()
router = APIRouter(
    prefix="/recruiter", tags=["Recruiter"], dependencies=[Depends(getCurrentUser)]
)


@router.get("/requisition/{id}")
def get_recruiter_requisition(id: int, request: Request, db: Session = Depends(get_db)):
    try:
        response = requsition_service.get_recruiter_requisition_detail(request, id, db)
        return ApiSuccessResponse(message="Success", data=response)
    except EntityNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post(
    "/requisitions",response_model=ApiSuccessResponse[RequsitionsResponseSchema]
)
async def get_requisition(
    request: Request,
    payload: RequisitionListingPayloadSchema,
    db: Session = Depends(get_db),
):
    try:
        requsitions = requsition_service.get_recruiter_requisitions(
            request, payload, db
        )
        return ApiSuccessResponse(
            message="Requisitions fetched successfully",
            data=requsitions,
        )
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.post("/create-requisition")
async def create_requisition(
   request: Request,
   requisition: RequisitionCreateSchema,
   background_tasks: BackgroundTasks,
   db: Session = Depends(get_db),
):
    try:
        send_cold_email = "send_cold_email" in request.query_params

        # send_cold_email_to_recruiter.delay(77)


        response = requsition_service.create_requisition(
            request,background_tasks, requisition, db
        )

        # create_recommended_candidates_from_similar_requisitions.delay(77)
        if send_cold_email:
            chain(
                add_llm_requisition_detail.s(
                    response["id"], response["long_description"]
                ),
                create_evaluation_criteria.si(response["id"]),
                create_requisition_vector_embeddings.si(response["id"]),
                create_recommended_candidates_from_similar_requisitions.si(
                    response["id"]
                ),
                send_cold_email_to_recruiter.si(response["id"]),
            ).apply_async()
        else:
            chain(
                add_llm_requisition_detail.s(
                    response["id"], response["long_description"]
                ),
                create_evaluation_criteria.si(response["id"]),
                create_requisition_vector_embeddings.si(response["id"]),
                create_recommended_candidates_from_similar_requisitions.si(
                    response["id"]
                ),
            ).apply_async()

        return ApiSuccessResponse(message="Requisition created successfully", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post("/update-requisition/{id}")
async def update_requisition(
    request: Request,
    id: int,
    requisition: RequisitionCreateSchema,
    db: Session = Depends(get_db),
):
    try:
        response = requsition_service.update_requisition(request, id ,requisition, db)
        return ApiSuccessResponse(message="Requisition updated successfully", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.delete("/delete-requisition/{id}")
async def delete_requisition(
    request: Request,
    id: int,
    db: Session = Depends(get_db),
):
    try:
        response = requsition_service.delete_requisition(request, id , db)
        return ApiSuccessResponse(message="Requisition deleted successfully", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.get("/organization-detail")
def get_organization_detail(
    request: Request,
    db: Session = Depends(get_db),
):
    try:
        print("get_organization_detail")
        response = organization_detail_service.get_organization_detail(request, db)
        return ApiSuccessResponse(message="Organization fetchedd successfully", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post("/update-organization/{id}")
def update_organization_detail(
    request: Request,
    id: int,
    organization: OrganizationDetailsCreateSchema,
    db: Session = Depends(get_db),
):
    try:
        response = organization_detail_service.update_orgacnization_detail(request, id, organization, db)
        return ApiSuccessResponse(message="Organization details updated successfully", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.post("/update-requisition-status/{requisition_id}")
async def update_requisition_status(
    request: Request,
    requisition_id: int,
    payload: RequisitionStatusUpdateSchema,
    db: Session = Depends(get_db),
):
    try:
        response = requsition_service.update_requisition_status(
            request, requisition_id, payload.status, db
        )
        return ApiSuccessResponse(message="Requisition status updated successfully", data=response)
    except EntityNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.get(
    "/get-interviews-by-requisition-organization",
    response_model=ApiSuccessResponse[RequisitionsInterviewResponse],
)
async def get_requisition_interviews(
    request: Request,
    db: Session = Depends(get_db),
):
    try:
        response = requsition_service.get_requisition_interviews(
            request, db
        )
        return ApiSuccessResponse(
            message="Interviews fetched successfully", data=response
        )
    except EntityNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
