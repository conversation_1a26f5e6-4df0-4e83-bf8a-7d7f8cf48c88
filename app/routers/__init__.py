from routers import requisition_skill
from routers import requisition
from routers import (
    admin,
    auth,
    users,
    read_skills,
    organization,
    interview,
    file_manager,
    interview_email,
    profession,
    job_dispatch,
    integrations,
)
from routers import change_preeval_algo
from routers import search_recommendation
from routers import interview_prep
from routers.recruiter import candidate
from routers.recruiter import requisition as recruiter_requisition
from routers.recruiter import organization
# from routers.recruiter.organization import organization as recruiter_organization
from routers import automation
from routers import activity
from routers import city
from routers import update_automation
from routers import country
from routers import interview_prep
from routers import jd_tool
from routers import company_industry
from routers import interview_prep_UNI

from routers import outreach
from routers import opensearch_migration
from routers import location_service
from routers import jd_tool

from routers import qc_automation
from routers import dropdown
from routers.recruiter import vapi
from routers import bulk_router
from routers import opensearch
from routers.recruiter import vapi


routers = [
    admin.router,
    auth.router,
    interview_prep_UNI.router,
    users.router,
    jd_tool.router,
    # resume.router,
    requisition.router,
    interview_prep.router,
    requisition_skill.router,
    read_skills.router,
    organization.router,
    interview.router,
    candidate.router,
    recruiter_requisition.router,
    file_manager.router,
    automation.router,
    activity.router,
    interview_email.router,
    city.router,
    interview_prep.router,
    jd_tool.router,
    organization.router,
    profession.router,
    update_automation.router,
    country.router,
    job_dispatch.router,
    company_industry.router,
    outreach.router,
    opensearch_migration.router,
    search_recommendation.router,
    change_preeval_algo.router,
    location_service.router,
    qc_automation.router,
    dropdown.router,
    vapi.router,
    bulk_router.router,
    integrations.router,
    opensearch.router,
    bulk_router.router,
    vapi.router,

]
