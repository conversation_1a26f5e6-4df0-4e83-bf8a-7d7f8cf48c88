from .common_imports import *
import logging
from services.interview.interview_email import InterviewEmailServiceClass
from services.user.user_interview import UserInterviewServiceClass

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/interview-email", tags=["InterviewEmail"]
)


interview_email_service_object = InterviewEmailServiceClass()


@router.get(
    "/get-missed-interview-users"
)
def get_missed_interview_users(
    db: Session = Depends(get_db),
):
    try:
        print("get_missed_interview_users")
        response = interview_email_service_object.get_users_with_interview_today(
            db, 'prev_day')
        return response
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get(
    "/get-half-hour-interview-users"
)
def get_half_hour_interview_users(
    db: Session = Depends(get_db),
):
    try:
        print("get_half_hour_interview_users")
        response = interview_email_service_object.get_users_with_interview_today(
            db, 'half-hour')
        return response
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get(
    "/get-today-interview-users"
)
def get_today_interview_users(
    db: Session = Depends(get_db),
):
    try:
        print("get_half_hour_interview_users")
        response = interview_email_service_object.get_users_with_interview_today(
            db, 'today')
        return response
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get("/send-feedback-to-completed-interviews/{requisition_id}")
def send_feedback_to_completed_interviews(
    requisition_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
):
    try:
        background_tasks.add_task(
            interview_email_service_object.send_feedback_to_completed_interviews,
            requisition_id,
            db,
        )
        return True
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

