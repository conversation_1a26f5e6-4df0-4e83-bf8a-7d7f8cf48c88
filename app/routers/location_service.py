from .common_imports import *
from sqlalchemy.orm import Session
from database import get_db
import logging
from services.location_service import LocationService
from typing import Dict
logger = logging.getLogger(__name__)


router = APIRouter()


@router.post("/get-location-match")
def location_match(
    request: Request,inputData: Dict[str, str], db: Session = Depends(get_db)
):
    try:
        
        response = LocationService().get_location(inputData.get("city"),inputData.get("country_code"),inputData.get("state"),db)
        return response
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

