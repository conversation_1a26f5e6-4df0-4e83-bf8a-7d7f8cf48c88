from .common_imports import *
from services.city import CityServiceClass
from schemas.city_country import CityResponseSchema
import logging
from services.interview.interview_email import InterviewEmailServiceClass
from database import get_mongodb_client
from celery_tasks.interview_email_tasks import testing_task
from celery_tasks.scrapping_tasks import scrapping_dog_jobs_to_mongo
from celery_tasks.onboarding_tasks import send_cold_email_to_recruiter
from services.requisition.requisition_similarity import RequisitionSimilarity
logger = logging.getLogger(__name__)

router = APIRouter()

city_service_object = CityServiceClass()

@router.get(
    "/cities"
    # response_model=ApiSuccessResponse[CityResponseSchema]
)
def get_cities(
    request: Request,
    db: Session = Depends(get_db),
):
    try:
        response = city_service_object.get_cities(request, db)
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.get("/get-requisition-cities")
def get_requisition_cities(
    request: Request,
    db: Session = Depends(get_db),
):
    try:
        response = city_service_object.get_requisition_cities(request, db)
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
