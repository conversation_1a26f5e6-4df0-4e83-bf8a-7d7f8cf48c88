from .common_imports import *
from sqlalchemy.orm import Session
from services.filemanager.filemanager import FileManagerClass
from services.external.openai import OpenAIServiceClass
from services.external.opensearch import OpenSearchServiceClass
from dependencies import (
    OPENSEARCH_RESUME_INDEX,
    AWS_RESUME_FOLDER,
)
from fastapi import Query
from database import get_db
from services.parser.text_extractor_service import TextExtractorService
from dependencies import MAIL_CONFIG, OPENSEARCH_CPA_INDEX
import logging
from services.change_preeval_algo import PreEvalServiceClass
logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/cpa", tags=["CPA"]
)

preevalservice = PreEvalServiceClass()


@router.post("/convert-resume")
def upload_resume(
    request: Request, email: str = Query(...), file: UploadFile = File(...), db: Session = Depends(get_db)
):
    text_extractor_service = TextExtractorService()

    if not text_extractor_service.is_supported_extension(file.filename):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str("Unsupported file type."),
        )
    try:
        file_manager_object = FileManagerClass()

        # store file in temp folder
        temp_file_path = file_manager_object.save_temp_file(file)
        file_path = temp_file_path.get("filePath")

        # Check if file path is valid
        if not file_path:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str("Failed to save the file."),
            )

        # Extract text from the file
        parser_text = text_extractor_service.extract_text(file_path=file_path)

        # Check if parser text is valid
        if not parser_text:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str("Failed to extract text from the file."),
            )

        # Use OpenAI to extract fields from the text
        open_ai_service_object = OpenAIServiceClass()
        opensearch_service = OpenSearchServiceClass(index_name=OPENSEARCH_CPA_INDEX)
        llmResponse = open_ai_service_object.extract_fields_from_text(
            parser_text)
        inserted_data = preevalservice.convert_resume_to_opensearch_format_and_insert(llmResponse, email)
                
        return ApiSuccessResponse(message="Success", data=inserted_data)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post("/get-similar-candidates")
def get_similar_candidates(
    req_id: str
):
    try:
        repsonse= preevalservice.get_similar_candidates(requistion_id=req_id)

        return ApiSuccessResponse(message="Success", data=repsonse)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

