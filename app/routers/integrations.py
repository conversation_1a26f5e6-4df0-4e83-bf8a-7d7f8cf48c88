from .common_imports import *
from services.external.outreach.exa import ExaServiceClass
from schemas.exa import ExaQueryPayload

router = APIRouter(
    prefix="/integrations",
    tags=["Integrations"],
)



@router.post("/exa/get-candidates")
async def save_video(
    request: Request,
    payload: ExaQueryPayload,
):
    try:
        # return payload
        exa_service = ExaServiceClass()
        response = exa_service.get_candidates(payload)
        
        return ApiSuccessResponse(
            message="Data retrieved successfully",
            data=response
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )