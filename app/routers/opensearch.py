from .common_imports import *
from services.external.opensearch import OpenSearchServiceClass

router = APIRouter(prefix="/opensearch", tags=["Opensearch"])


@router.post(
    "/update-candidate-intake/{document_id}")
def getUser(
    document_id: str,
    payload: dict
):
    try:
        opensearch_service = OpenSearchServiceClass(None)

        response = opensearch_service.update_candidate_intake(
            document_id, payload)

        if response:
            return ApiSuccessResponse(message="Document updated", data=response)
        else:
            return ApiFailureResponse(
                message="Failed to update document"
            )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


