from helper.helper import format_person_data
from typing import Dict, Any, Optional, List, Union
from models.shortlisted_candidate import ShortlistedCandidate
import logging
import re
from models.user import User
from models.question_feedback import QuestionFeedback
from services.external.openai import OpenAIServiceClass
from services.external.opensearch import OpenSearchServiceClass
from dependencies import (
    OPENSEARCH_RC_INDEX
)

logger = logging.getLogger(__name__)
open_ai_service_object = OpenAIServiceClass()


class SearchRecommendationService:

    def get_similarity_search(self, query, db, level=None, page=1, per_page=10, most_recent=None, city_id=None, state_id=None, country_id=None):
        try:
            # Map level to experience range
            exp_gte, exp_lte = self._get_experience_range(level)

            # Build filter data
            initial_filter_data = {
                "exp_greater_than_or_equals": exp_gte,
                "exp_less_than_or_equals": exp_lte,
                "city_id": city_id,
                "country_id": country_id,
                "state_id": state_id
            }

            query = query.lower()

            # Classify query context using OpenAI
            classification = open_ai_service_object.classify_query_context(query)

            pins = {key: value for key, value in classification['pins'].items() if value is not None}

            if not classification:
                logger.error("Couldn't get candidates, please try another query")
            # Get OpenAI embeddings for the query
            query_vector = open_ai_service_object.get_embeddings(classification['rephrased_query']).get('vector', None)
            if not query_vector:
                logger.error("Couldn't embed your query")
                return {}
            
            filter_data = self._filter_data(initial_filter_data, classification)
            # Run similarity search based on context
            candidates = self._search_candidates(classification, query_vector, filter_data, page, per_page, most_recent, query)
            
            if candidates:                
                refined_candidates = self._get_candidate_info_from_postgres(candidates, db)
                refined_candidates['pins'] = pins

                logger.info("Completed searching candidates against user query.")
                return refined_candidates
            else:
                 logger.error("Couldn't match your query, please rephrase")
                 return {}

        except Exception as e:
            logger.error(f"Error in get_similarity_search: {str(e)}")
            return {"error": str(e)}

    @staticmethod
    def _filter_data(initial_filter_data: Dict[str, Any], classification: Dict[str, Any]) -> Dict[str, Any]:
        if not isinstance(classification, dict):
            classification = classification.dict()

        filters: Optional[Dict[str, Any]] = classification.get("filters") or {}

        filter_to_keys = {
            "city": "city_id",
            "state": "state_id",
            "country": "country_id",
            "exp_greater_than_or_equals": "exp_greater_than_or_equals",
            "exp_less_than_or_equals": "exp_less_than_or_equals",
            "organization": "organization",
            "industry": "industry",
            "location_preferences":"location_preferences",
            "expected_salary": "expected_salary",
            "notice_period": "notice_period"
        }

        result = {}

        # Handle experience filters together to prevent duplicates
        exp_gte = initial_filter_data.get("exp_greater_than_or_equals")
        exp_lte = initial_filter_data.get("exp_less_than_or_equals")

        if exp_gte is not None or exp_lte is not None:
            if exp_gte is not None:
                result["exp_greater_than_or_equals"] = exp_gte
            if exp_lte is not None:
                result["exp_less_than_or_equals"] = exp_lte
        else:
            # Pull from classification only if not overridden
            gte = filters.get("exp_greater_than_or_equals")
            lte = filters.get("exp_less_than_or_equals")

            if gte is not None and lte is not None and gte == lte:
                # Treat as exact match
                result["exp_greater_than_or_equals"] = gte
                result["exp_less_than_or_equals"] = lte
            else:
                if gte is not None:
                    result["exp_greater_than_or_equals"] = gte
                if lte is not None:
                    result["exp_less_than_or_equals"] = lte

        # Handle all other filters
        for key, internal_key in filter_to_keys.items():
            if key.startswith("exp_"):  # already handled above
                continue

            if key in ["city", "state", "country"]:
                if initial_filter_data.get(internal_key) not in [None, 0]:
                    result[internal_key] = initial_filter_data[internal_key]
                elif filters.get(key) is not None:
                    result[key] = filters[key]  # fallback to raw name
            else:
                # organization and industry
                if initial_filter_data.get(internal_key):
                    result[internal_key] = initial_filter_data[internal_key]
                elif filters.get(key):
                    result[internal_key] = filters[key]

        return result


    @staticmethod
    def _get_candidate_info_from_postgres(candidates, db):
        for candidate_id, candidate_info in candidates['items'].items():
            logger.info("Processing candidate_id: %s", candidate_id)
            candidate_data = candidate_info.get("candidate_data")
            if not candidate_data:
                continue

            person_id = candidate_data.get("person_id")
            if not person_id:
                continue

            # Get profile_pic from Users table
            user = db.query(User).filter(User.id == person_id).first()
            profile_pic = user.profile_pic if user else None
            expected_salary = user.expected_salary if user else None
            location_preferences = user.location_preferences if user else None
            notice_period = user.notice_period if user else None

            shortlist = db.query(ShortlistedCandidate).filter(ShortlistedCandidate.candidate_id == person_id).first()
            interview_feedback_status = shortlist.status if shortlist else None
            
            feedback_data = []
            if candidate_data.get("feedback"):
                # Get feedback list from QuestionFeedback table
                feedback_rows = (
                    db.query(QuestionFeedback)
                    .filter(QuestionFeedback.user_id == person_id)
                    .order_by(QuestionFeedback.id.asc())
                    .all()
                )
                
                feedback_data = [
                    {
                        "question": row.summarized_question if getattr(row, "summarized_question", None) else getattr(row, "question", ""),
                        "answer": getattr(row, "answer", ""),
                        "video_link": getattr(row, "video_link", None)
                    }
                    for row in feedback_rows
                ]

            # Append the retrieved data to candidate_data
            candidate_data["profile_pic"] = profile_pic
            candidate_data["expected_salary"] = expected_salary
            candidate_data["location_preferences"] = location_preferences
            candidate_data["notice_period"] = notice_period
            candidate_data["feedback_data"] = feedback_data
            candidate_data["interview_feedback_status"] = interview_feedback_status

            # logger.info(expected_salary, location_preferences, notice_period, profile_pic)

        return candidates



    @staticmethod
    def _get_experience_range(level):
        level_mapping = {
            "Junior Level": (1, 2),
            "Mid Level": (2, 3),
            "Senior Level": (3, 4),
            "Manager/Lead Level": (5, None)
        }

        if level in level_mapping:
            exp_range = level_mapping[level]
            logger.info(f"Level: {level}, Exp Range: {exp_range}")
            return exp_range
        elif level:
            logger.warning(f"Unrecognized level '{level}', defaulting to no experience filters.")
        else:
            logger.info("No level provided, skipping experience filters.")
        return None, None

   
    @staticmethod
    def _extract_location(location):
        if location:
            match = re.match(r"^(.*?),\s*(.*?)$", location)
            if match:
                city, country = match.groups()
                logger.info(f"Extracted City: {city}, Country: {country}")
                return city, country
            else:
                logger.warning(f"Invalid location format: {location}")
        return None, None

    @staticmethod
    def _search_candidates(classification, query_vector: list, filter_data: dict = None, page: int = 1, per_page: int = 10, most_recent: bool= None, query: str=None):
        opensearch_service = OpenSearchServiceClass(OPENSEARCH_RC_INDEX)
        logger.info(filter_data)
        if classification.get('candidate_skills') and not classification.get('work_experience'):
            logger.info("Searching based on skills only...")
            return opensearch_service.get_search_candidates(query_vector, mode="skills", filter_data=filter_data, page=page, page_size=per_page, most_recent=most_recent, user_query=query)
        elif classification.get('work_experience') and not classification.get('candidate_skills'):
            logger.info("Searching based on experience only...")
            return opensearch_service.get_search_candidates(query_vector, mode="exp", filter_data=filter_data, page=page, page_size=per_page, most_recent=most_recent, user_query=query)
        elif classification.get('candidate_skills') and classification.get('work_experience'):
            logger.info("Searching based on both skills and experience...")
            return opensearch_service.get_search_candidates(query_vector, mode="both", filter_data=filter_data, page=page, page_size=per_page, most_recent=most_recent, user_query=query)
        else:
            logger.warning("No valid classification found for query.")
            return None
    def get_matched_filters(self, user_id, filters):
        try:
            opensearch_service = OpenSearchServiceClass(OPENSEARCH_RC_INDEX)
            person_data = "Not available"
            try:
                person_data = opensearch_service.get_candidate_data_from_opensearch(user_id)
            except Exception:
                pass  # Keep default value "Not available"

            matches = open_ai_service_object.match_search_filters(person_data, filters)
            return matches
        except Exception as e:
            logger.error(f"Error in get_matched_filters: {str(e)}")
            raise e
