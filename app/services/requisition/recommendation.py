from fastapi.encoders import jsonable_encoder
from models.skill import Skill
from models.requisition import Requisition
from models.organization import Organization
from models.recommended_candidates import RecommendedCandidates
from services.external.openai import OpenAIServiceClass
from services.interview.interview import InterviewServiceClass
from services.requisition.requisition import RequisitionServiceClass
from services.user.user import UserServiceClass
from services.requisition.evaluation_criteria import EvaluationCriteria
from services.requisition.requisition_similarity import RequisitionSimilarity
from models.interview_feedback import InterviewFeedback
from dependencies import CANDIDATE_THRESHOLD_SCORE
from database import sessionLocal
from celery_config import celery_app
import logging
from services.candidate_recommendation import CandidateRecommendationService

rec_service = CandidateRecommendationService()
logger = logging.getLogger(__name__)


class CandidateRecommendation:
    def __init__(self):
        self.openai_service = OpenAIServiceClass()
        self.interview_service = InterviewServiceClass()
        self.requisition_service = RequisitionServiceClass()
        self.user_service = UserServiceClass()
        self.evaluation_criteria_service = EvaluationCriteria()

    def evaluate_and_store_recommendation(self, interviews: list[object], requisition_id: int, db):
        """
        Evaluate a list of interviews against a requisition and store the recommendation
        in the database.

        Args:
            interviews (list[Interview]): The list of interviews to evaluate.
            requisition_id (int): The ID of the requisition to evaluate against.
            db (Session): The SQLAlchemy session to use.

        Returns:
            True if the evaluation was successful.
        """

        for interview in interviews:
            try:
                candidate_recommendation = db.query(RecommendedCandidates).filter(
                    RecommendedCandidates.user_id == interview['user_id'],
                    RecommendedCandidates.requisition_id == requisition_id,
                    RecommendedCandidates.interview_id == interview['id']
                ).first()

                requisition_profession_id = db.query(Requisition).filter(
                    Requisition.id == requisition_id).first().profession_id
                interview_profession_id = db.query(Requisition).filter(
                    Requisition.id == interview['requisition_id']).first().profession_id

                if requisition_profession_id != interview_profession_id:
                    logger.info(
                        f"Profession mismatch for requisition ID: {requisition_id}, interview ID: {interview['id']}, candidate ID: {interview['user_id']}")
                    continue

                if not candidate_recommendation:
                    logger.info(
                        f"Evaluating requisition ID: {requisition_id}, interview ID: {interview['id']}, candidate ID: {interview['user_id']}")

                    interview_transcript = self.interview_service.get_interview_transcript(
                        interview['id'], db)

                    resume = self.user_service.get_user_by_id(
                        interview['user_id'], db
                    )

                    user_skills = [
                        db.query(Skill).filter(
                            Skill.id == skill.skill_id).first().name
                        for skill in resume.skills
                    ]

                    education = ", ".join(
                        [
                            f"{education.degree} - {education.field} ({education.start_date} - {education.end_date or 'present'})"
                            for education in resume.educations
                        ]
                    )

                    work_experience = [
                        {
                            "organization": db.query(Organization).filter(Organization.id == experience.organization_id).first().name,
                            "position": experience.job_title,
                            "duration": f"{experience.from_date} - {experience.to_date or 'present'}",
                            "description": experience.description,
                        }
                        for experience in resume.experiences
                    ]

                    resume = {
                        "name": f"{resume.first_name} {resume.last_name}",
                        "city": resume.city.name,
                        "country": resume.city.country.name,
                        "total_experience": f"{resume.total_experience}",
                        "skills": user_skills,
                        "education": education,
                        "work_experience": work_experience,
                        "about": resume.about
                    }

                    requisition = self.requisition_service.get_requisition_by_id(
                        requisition_id, db).evaluation_criteria

                    evaluation = self.openai_service.evauate_candidate_against_requisition(
                        interview_transcript, resume, requisition)

                    candidate_recommendation = RecommendedCandidates(
                        user_id=interview['user_id'],
                        requisition_id=requisition_id,
                        interview_id=interview['id'],
                        score=evaluation['evaluation']['weighted_percentage'],
                        feedback=evaluation,
                        satisfies_binary_requirements=evaluation['evaluation']['is_satisfactory'],
                    )

                    db.add(candidate_recommendation)
                    db.commit()
                    db.refresh(candidate_recommendation)

                    logger.info(
                        f"Successfully evaluated requisition ID: {requisition_id}, interview ID: {interview['id']}, candidate ID: {interview['user_id']}")
                else:
                    logger.info(
                        f"Interview already evaluated for requisition ID: {requisition_id}, interview ID: {interview['id']}, user ID: {interview['user_id']}")
            except Exception as e:
                logger.info(
                    f"Error in evaluating requisition ID: {requisition_id}, interview ID: {interview['id']}, candidate ID: {interview['user_id']}: {e}"
                )

        return True

    def create_recommendations(self, requisition_id: int, db) -> list[str]:
        """
        Evaluate all completed interviews for a given requisition and store the result in the database.

        Args:
            requisition_id (int): The ID of the requisition to evaluate against.
            db (Session): The database session to use for the evaluation.

        Raises:
            Exception: If an error occurs during the evaluation process.

        Returns:
            list[str]: A list of task IDs for the background tasks.
        """

        logger.info(
            f"Creating recommendations for requisition ID: {requisition_id}")

        try:
            chunk_size = 20
            offset = 0
            task_ids = []

            while True:
                interviews = self.interview_service.get_completed_interviews(
                    db, chunk_size, offset
                )

                if not interviews:
                    break

                evaluate_task = evaluate.delay(
                    jsonable_encoder(interviews), requisition_id)
                task_ids.append(evaluate_task.task_id)

                offset += chunk_size

            logger.info(
                f"Background jobs added for requisition ID: {requisition_id}")

            return task_ids
        except Exception as e:
            logger.info(
                f"Error in creating recommendations for requisition ID: {requisition_id}: {e}")
            raise e

    def get_recommendations(self, requisition_id, db) -> list[RecommendedCandidates]:
        try:
            logger.info(
                f"Getting recommendations for requisition ID: {requisition_id}")

            recommended_candidates = db.query(RecommendedCandidates).filter(
                RecommendedCandidates.requisition_id == requisition_id
            ).all()

            return recommended_candidates
        except Exception as e:
            logger.error(
                f"Error getting recommendations for requisition ID: {requisition_id}")
            raise e

    def create_evaluation_criteria(self, requisition_id: int, db) -> dict:
        """
        Create evaluation criteria.

        Args:
            requisition_id (int): The ID of the requisition.
            db (Session): The database session to use.

        Returns:
            dict: A dictionary containing the status of evaluation criteria creation.
        """
        try:
            evaluation_criteria_response = self.evaluation_criteria_service.create_evaluation_criteria(
                requisition_id, db)

            if not evaluation_criteria_response:
                raise Exception("Failed to create evaluation criteria")

            return True
        except Exception as e:
            logger.error(
                f"Error in creating evaluation criteria and recommendations for requisition ID {requisition_id}: {e}")
            return False

    def create_recommended_candidates_from_similar_requisitions(
        self, requisition_id, db
    ):
        try:
            recommended_candidates = rec_service.get_similarity(requisition_id, db)
            
            for candidate in recommended_candidates.values():  # Iterate over values, not keys
                recommended_candidate = RecommendedCandidates(
                    interview_id=candidate['candidate_data']['interview_id'],
                    requisition_id=requisition_id,
                    user_id=candidate['candidate_data']['person_id'],
                    feedback=candidate['candidate_data'].get('feedback', ''),  # Use .get() to avoid KeyError
                    score=candidate['total_score'],
                    satisfies_binary_requirements=True,
                )
                db.add(recommended_candidate)

            # Commit the new recommended candidates records
            db.commit()
        except Exception as e:
            logger.error(
            f"Error evaluating interviews for requisition ID: {requisition_id}: {e}")
            raise e
