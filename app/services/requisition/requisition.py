from models.requisition import Requisition
from models.city import City
from models.organization import Organization
from models.interview_feedback import InterviewFeedback
from models.recommended_candidates import RecommendedCandidates
from models.user_requisition import UserRequisition
from models.user_organization import UserOrganization
from models.professions import Professions
from models.shortlisted_candidate import ShortlistedCandidate
from services.organization.organization import OrganizationServiceClass
from services.city import CityServiceClass
from services.country import CountryServiceClass
from services.state import StateServiceClass
from celery_config import celery_app
from schemas.requisition import RequisitionCreateSchema
from schemas.vapi import RecruiterRequisitionCreateSchema


from sqlalchemy.orm import Session, joinedload
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import or_
from sqlalchemy.dialects import postgresql
from services.parser.requisition_parser import RequisitionParserServiceClass
from fastapi import HTTPException
from fastapi.encoders import jsonable_encoder
import math
import time
import json
from custom_exceptions import EntityNotFoundException
from custom_exceptions import EntityAlreadyExistsException

import logging
from dependencies import CANDIDATE_THRESHOLD_SCORE,RECOMMENDED_CANDIDATE_THRESHOLD_SCORE
from helper.helper import get_shortlisted_candidates_ids, get_interviewed_candidates_count, get_recommended_candidates_count

logger = logging.getLogger(__name__)

organization_service = OrganizationServiceClass()
city_service = CityServiceClass()
country_service = CountryServiceClass()
state_service = StateServiceClass()
req_parser=RequisitionParserServiceClass


class RequisitionServiceClass:

    def get_requisitions(self, request, db):
        try:
            page = int(request.query_params.get("page", 1))
            per_page = int(request.query_params.get("per_page", 10))
            q = request.query_params.get("q")
            organization_id = request.query_params.get("organization")
            query = (
                db.query(Requisition)
                .options(
                    joinedload(Requisition.organization),
                    joinedload(Requisition.city),
                )
                .filter(Requisition.status == Requisition.ACTIVE)
                .order_by(Requisition.id.desc())
            )
            if q:
                query = query.filter(
                    or_(
                        Requisition.title.ilike(f"%{q}%"),
                        Requisition.organization.has(
                            Organization.name.ilike(f"%{q}%")),
                        Requisition.city.has(City.name.ilike(f"%{q}%")),
                        Requisition.location_type.ilike(f"%{q}%"),
                    )
                )
            city_name = request.query_params.get("city")

            if organization_id:
                query = query.filter(
                    Requisition.organization_id == organization_id)
            if city_name:
                query = query.filter(Requisition.city.has(City.name.ilike(f"%{city_name}%"))) #query.filter(Requisition.city_id == city_id)

            total = query.count()
            requisitions = query.offset(
                (page - 1) * per_page).limit(per_page).all()

            requisition_responses = []
            if hasattr(request.state, "user"):
                for requisition in requisitions:
                    user_requisition = (
                        db.query(UserRequisition)
                        .filter(
                            UserRequisition.requisition_id == requisition.id,
                            UserRequisition.user_id == request.state.user.id,
                        )
                        .first()
                    )
                    requisition_data = jsonable_encoder(requisition)
                    if user_requisition:
                        requisition_data["user_requisition"] = {
                            "id": user_requisition.id,
                            "user_id": user_requisition.user_id,
                            "requisition_id": user_requisition.requisition_id,
                            "is_saved": user_requisition.is_saved,
                            "is_applied": user_requisition.is_applied,
                            "status": user_requisition.status,
                        }
                    else:
                        requisition_data["user_requisition"] = None
                    requisition_responses.append(requisition_data)
            else:
                requisition_responses = requisitions
            return {
                "pagination": {
                    "total": total,
                    "last_page": math.ceil(total / per_page),
                    "page": page,
                    "per_page": per_page,
                },
                "items": jsonable_encoder(requisition_responses)
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    def add_requisition(self, req_body, db):
        try:
            logger.info("add req init")
            company_name = req_body.get("company")
            parsed_req = req_body.get("parsed_req")
            link = req_body.get("link")
            profession_id = req_body.get("profession_id")

            job_details = parsed_req.get("job_details", {})
            title = req_body.get("title")
            location = parsed_req.get("location", {})
            city = location.get("city", None)
            country = location.get("country", None)
            state = location.get("state", None)
            technical_req=req_body.get("cleaned_req").get("requirements").get("technical_requirements")
            binary_req=req_body.get("cleaned_req").get("requirements").get("binary_requirements")

            if country:
                country = country_service.add_country(country, db)
            if city:
                city = city_service.add_city(city, country.id, db)
            if state:
                state = state_service.add_state(state, country.id, db)

            description = job_details.get("description", None)
            requirements = job_details.get("requirements_and_responsibilities")
            # responsibilities = job_details.get("responsibilities")
            salary_range = job_details.get("salary_range", None)
            salary_details = job_details.get("salary_details", None)
            location_type = job_details.get("location_type", None)
            job_type = job_details.get("job_type", None)
            close_date = job_details.get("close_date", None)
            level = job_details.get("matched_level", None)
            organization_input = {
                "name": company_name,
                "city_id": city.id if city else None,
            }
            long_description = "<h3>Requirements and Responsibilities</h3>\n"
            long_description += "<ul>\n"

            for requirement in requirements:
                long_description += f"  <li>{requirement}</li>\n"

            long_description += "</ul>\n"

            organization = organization_service.add_organization(
                organization_input, db)

            db_requisition = Requisition(
                organization_id=organization.id,
                profession_id=profession_id,
                user_id=0,
                city_id=city.id if city else None,
                description=description,
                # requirements=json.dumps(
                #     requirements) if requirements else None,
                # responsibilities=json.dumps(
                #     responsibilities) if responsibilities else None,
                salary_details=salary_details,
                title=title,
                salary_range=salary_range,
                location_type=location_type,
                job_type=job_type,
                close_date=None if close_date == "null" else close_date,
                status=0,
                job_link=link,
                level=level,
                fine_tuning_status=0,
                llm_requistion_details=json.dumps(technical_req) if technical_req else None,
                binary_requirements=json.dumps(binary_req) if binary_req else None,
                long_description= long_description
            )
            # logger.info(f"DB Requisition: {db_requisition}")

            db.add(db_requisition)
            db.commit()
            db.refresh(db_requisition)

            logger.info(
                f"Inserted job description from {link} into the database.")
        except Exception as e:
            raise e

    def update_requisitions_fine_tuning_status(self, requisition_ids, db):
        try:
            updated_requisitions = []
            for requisition_id in requisition_ids:
                requisition = (
                    db.query(Requisition)
                    .filter(Requisition.id == requisition_id)
                    .first()
                )
                if requisition:
                    requisition.fine_tuning_status = 1
                    updated_requisitions.append(requisition)
                else:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Requisition ID {requisition_id} not found",
                    )

            db.commit()
            return updated_requisitions
        except Exception as e:
            raise e

    def get_requisition(self, request, id, db):
        try:
            requisition = (
                db.query(Requisition)
                .options(
                    joinedload(Requisition.organization), joinedload(
                        Requisition.city)
                )
                .filter(Requisition.id == id)
                .first()
            )
            requisition_data = jsonable_encoder(requisition)
            if hasattr(request.state, "user"):
                user_requisition = (
                    db.query(UserRequisition)
                    .filter(
                        UserRequisition.requisition_id == requisition.id,
                        UserRequisition.user_id == request.state.user.id,
                    )
                    .first()
                )
                if user_requisition:
                    requisition_data["user_requisition"] = {
                        "id": user_requisition.id,
                        "user_id": user_requisition.user_id,
                        "requisition_id": user_requisition.requisition_id,
                        "is_saved": user_requisition.is_saved,
                        "is_applied": user_requisition.is_applied,
                        "status": user_requisition.status,
                    }
                else:
                    requisition_data["user_requisition"] = None
            else:
                requisition_data["user_requisition"] = None
            return requisition_data
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    def get_requisition_by_id(self, id, db) -> Requisition:
        """
        Get a requisition by its id.

        Args:
            id (int): The id of the requisition to retrieve.
            db (Session): The database session to use.

        Returns:
            Requisition: The retrieved requisition, or None if no such id exists.

        Raises:
            Exception: If an unexpected error occurs.
        """

        try:
            requisition = (
                db.query(Requisition)
                .filter(Requisition.id == id)
                .first()
            )
            return requisition
        except Exception as e:
            raise e

    def get_requisition_by_title(self, title: str, db) -> Requisition:
        """
        Get a requisition by its title.

        Args:
            title (str): The title of the requisition to retrieve.
            db (Session): The database session to use.

        Returns:
            Requisition: The retrieved requisition, or None if no such title exists.

        Raises:
            Exception: If an unexpected error occurs.
        """

        try:
            requisition = (
                db.query(Requisition)
                .filter(Requisition.title == title)
                .first()
            )
            return requisition
        except Exception as e:
            raise e

    def get_recruiter_requisition_detail(self, request, id, db):
        try:
            requisition = (
                db.query(Requisition)
                .options(
                    joinedload(Requisition.city),
                    joinedload(Requisition.organization)
                ).filter(Requisition.id == id, Requisition.organization_id == request.state.user.organization_id)
                .first()
            )

            if requisition:
                shortlisted_candidate_ids = get_shortlisted_candidates_ids(requisition.id, db)
                requisition.interviewed_candidates = get_interviewed_candidates_count(requisition.id, shortlisted_candidate_ids ,db)
                requisition.recommended_candidates = get_recommended_candidates_count(requisition.id, shortlisted_candidate_ids ,db)
                return jsonable_encoder(requisition)
            else:
                raise EntityNotFoundException(
                    f"Requisition with id {id} not found.")
        except Exception as e:
            raise e

    def get_recruiter_requisitions(self, request, db):
        try:
            page = int(request.query_params.get("page", 1))
            per_page = int(request.query_params.get("per_page", 10))
            q = request.query_params.get("q")
            organization_id = request.state.user.organization_id

            query = (
                db.query(Requisition)
                .options(
                    joinedload(Requisition.organization),
                    joinedload(Requisition.city),
                )
                .filter(Requisition.organization_id == organization_id)
                .order_by(Requisition.id.desc())
            )
            if q:
                query = query.filter(
                    or_(
                        Requisition.title.ilike(f"%{q}%"),
                        Requisition.organization.has(
                            Organization.name.ilike(f"%{q}%")),
                        Requisition.city.has(City.name.ilike(f"%{q}%")),
                        Requisition.location_type.ilike(f"%{q}%"),
                    )
                )
            # if organization_id:
            #     query = query.filter(
            #         Requisition.organization_id == organization_id)
            total = query.count()
            requisitions = query.offset(
                (page - 1) * per_page).limit(per_page).all()

            for requisition in requisitions:
                shortlisted_candidate_ids = get_shortlisted_candidates_ids(
                    requisition.id, db
                )
                interviewed_candidates = get_interviewed_candidates_count(requisition.id, shortlisted_candidate_ids, db)
                recommended_candidates = get_recommended_candidates_count(requisition.id, shortlisted_candidate_ids, db)
                requisition.user_count = interviewed_candidates + recommended_candidates
            return {
                "pagination": {
                    "total": total,
                    "last_page": math.ceil(total / per_page),
                    "page": page,
                    "per_page": per_page,
                },
                "items": requisitions,
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    def create_requisition(self, request,background_tasks, requisition_data: RequisitionCreateSchema, db
        ):
        try:
            existing_organization = (
                db.query(Requisition)
                .filter(
                    Requisition.organization_id == request.state.user.organization_id,
                    Requisition.title == requisition_data.title,
                )
                .first()
            )
            if existing_organization:
                raise EntityAlreadyExistsException(
                    "Requisition with this title already exists"
                )
            requisition = Requisition(
                organization_id=request.state.user.organization_id,
                profession_id=requisition_data.profession_id,
                user_id=request.state.user.id,
                city_id=requisition_data.city_id,
                state_id=requisition_data.state_id,
                country_id=requisition_data.country_id,
                description=requisition_data.description,
                long_description=requisition_data.long_description,
                salary_details=requisition_data.salary_details,
                title=requisition_data.title,
                salary_range=requisition_data.salary_range,
                location_type=requisition_data.location_type,
                job_type=requisition_data.job_type,
                close_date=(
                    None
                    if requisition_data.close_date == "null"
                    else requisition_data.close_date
                ),
                status=1,  # what exactly is the purpose of requsiiton??
                job_link=requisition_data.job_link,
                level=requisition_data.level,
                fine_tuning_status=0,
                show_salary=(1 if requisition_data.show_salary else 0),
            )
            db.add(requisition)
            db.commit()
            db.refresh(requisition)
            return jsonable_encoder(requisition)
        except Exception as e:
            raise e
        
    def create_requisition_from_recruiter_conversation(self,organization_id,user_id,profession_id, requisition_data: RecruiterRequisitionCreateSchema, db,recruiter_info
        ):
        try:
            existing_requisition = (
                db.query(Requisition)
                .filter(
                    Requisition.organization_id == organization_id,
                    Requisition.title == requisition_data.title,
                )
                .first()
            )
            if existing_requisition:
                logger.info(f"Requisition is already present in the database")
                return jsonable_encoder(existing_requisition)


            requisition = Requisition(
                organization_id=organization_id,
                profession_id=profession_id,
                user_id=user_id,
                city_id=recruiter_info.get("city_id"),
                state_id=recruiter_info.get("state_id"),
                country_id=recruiter_info.get("country_id"),
                description=requisition_data.description,
                long_description=requisition_data.long_description,
                salary_details=requisition_data.salary_details,
                title=requisition_data.title,
                salary_range=requisition_data.salary_range,
                location_type=requisition_data.location_type,
                job_type=requisition_data.job_type,
                close_date=None,
                status=1,
                job_link=None,
                level=requisition_data.level if requisition_data.level else "Junior Level",
                fine_tuning_status=0,
                show_salary=(1 if requisition_data.show_salary else 0),
                llm_requistion_details=json.dumps(requisition_data.llm_requistion_details) if requisition_data.llm_requistion_details else None,
                binary_requirements=json.dumps(requisition_data.binary_requirements) if requisition_data.binary_requirements else None,
            )
            db.add(requisition)
            db.commit()
            db.refresh(requisition)
            return jsonable_encoder(requisition)
        except Exception as e:
            raise e


    def update_requisition(self, request,requisition_id, requisition_data: RequisitionCreateSchema, db):    
        try:
            requisition = db.query(Requisition).filter(Requisition.id == requisition_id, Requisition.user_id == request.state.user.id).first()
            # if requisition_data.organization_id is not None:
            #     requisition.organization_id = requisition_data.organization_id
            if requisition_data.title is not None:
                requisition.title = requisition_data.title
            if requisition_data.profession_id is not None:
                requisition.profession_id = requisition_data.profession_id
            if requisition_data.city_id is not None:
                requisition.city_id = requisition_data.city_id
            if requisition_data.description is not None:
                requisition.description = requisition_data.description
            if requisition_data.salary_range is not None:
                requisition.salary_range = requisition_data.salary_range
            if requisition_data.requirements is not None:
                requisition.requirements = json.dumps(requisition_data.requirements) 
            if requisition_data.responsibilities is not None:
                requisition.responsibilities = json.dumps(requisition_data.responsibilities)
            if requisition_data.salary_details is not None:
                requisition.salary_details = requisition_data.salary_details
            if requisition_data.location_type is not None:
                requisition.location_type = requisition_data.location_type
            if requisition_data.job_type is not None:
                requisition.job_type = requisition_data.job_type
            if requisition_data.job_link is not None:
                requisition.job_link = requisition_data.job_link
            if requisition_data.level is not None:
                requisition.level = requisition_data.level
            if requisition_data.fine_tuning_status is not None:
                requisition.fine_tuning_status = requisition_data.fine_tuning_status

            db.commit()
            db.refresh(requisition)
            return jsonable_encoder(requisition)
        except Exception as e:
            raise e

    def delete_requisition(self, request, requisition_id, db):
        try:
            requisition = db.query(Requisition).filter(Requisition.id == requisition_id, Requisition.user_id == request.state.user.id).first()
            if requisition:
                requisition.status = 0
                db.commit()
                db.refresh(requisition)
                return jsonable_encoder(requisition)
            else:
                raise EntityNotFoundException(f"Requisition with id {requisition_id} not found.")
        except Exception as e:
            raise e
