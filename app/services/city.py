from fastapi import HTTPException, status
from models.city import City
import json
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import func
from dependencies import returnResponse
from sqlalchemy.orm import joinedload
from fastapi.encoders import jsonable_encoder
from models.country import Country

import logging

logger = logging.getLogger(__name__)

class CityServiceClass:

    def get_cities(self, request, db):
        try:
            country_id = request.query_params.get("country_id")
            country_id = (
                int(country_id) if country_id and country_id.isdigit() else None
            )
            if country_id:
                cities = (
                    db.query(City)
                    .filter(City.country_id == country_id)
                    .options(joinedload(City.country))
                    .all()
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="country_id is required",
                )
            return jsonable_encoder(cities)
        except Exception as e:
            raise e

    def add_city(self, city_name, country_id, db):
        try:
            existing_city = db.query(City).filter(City.name == city_name).first()
            if existing_city:
                return existing_city
            if city_name:
                city = City(name=city_name, status=0, country_id=country_id)
                db.add(city)
                db.commit()
                db.refresh(city)
                return city
        except Exception as e:
            raise e

    def remove_duplicate_cities(self, s3link, db):
        try:
            import requests
            from models.user import User
            from models.requisition import Requisition

            cities_with_duplicates = []
            total_users_updated = 0
            total_requisitions_updated = 0
            response = requests.get(s3link)
            cities = response.json()

            # filter out duplicates
            for city in cities:
                if city.get(
                    "duplicates"
                ):  # Checks if duplicates exists and is not empty
                    cities_with_duplicates.append(city)
            with db.begin():
                # Remove duplicates from database
                for city in cities_with_duplicates:
                    # Get the primary city
                    primary_city = city.get("primary")
                    for duplicate in city.get("duplicates"):

                        # Check if the duplicate exists in users
                        users = (
                            db.query(User).filter(User.city_id == duplicate["id"]).all()
                        )
                        for user in users:
                            total_users_updated += 1
                            # Update the user's city to the primary city
                            user.city_id = primary_city["id"]
                            db.flush()

                        # Check if the duplicate exists in requisitions
                        requisitions = (
                            db.query(Requisition)
                            .filter(Requisition.city_id == duplicate["id"])
                            .all()
                        )
                        for requisition in requisitions:
                            total_requisitions_updated += 1
                            # Update the requisition's city to the primary city
                            requisition.city_id = primary_city["id"]
                            db.flush()
                        # Remove city from database
                        db.query(City).filter(City.id == duplicate["id"]).delete()
                # End transaction
                db.commit()

            return {
                'total_users_updated': total_users_updated,
                'total_requisitions_updated': total_requisitions_updated,
            }
        except Exception as e:
            raise e

    def get_cities_by_country_id(self, country_id, db):
        try:
            if country_id:
                cities = (
                    db.query(City)
                    .filter(City.country_id == country_id)
                    .options(joinedload(City.country))
                    .all()
                )
            else:
                cities = db.query(City).options(joinedload(City.country)).all()
            return jsonable_encoder(cities)
     
        except Exception as e:
            raise e
        

    def get_city_id(self,city_name, country_name, db):
        try:
            logger.info(f"Getting city id started")
            country_id = 0
            city_id = 0
            data = {}
            
            # Update country id if country exist
            if country_name is not None and country_name != "" and country_name.lower() != "null":
                country_object = db.query(Country).filter(
                    Country.name == country_name).first()
                if country_object:
                    country_id = country_object.id
                
                # Update city id if city exist
                if country_id > 0 and city_name is not None and city_name != "" and city_name.lower() != "null":
                    city_object = db.query(City).filter(City.name == city_name).first()
                    if city_object:
                        city_id = city_object.id
                        state_id=city_object.state_id
            
                data = {"country_name": country_name, "city_name": city_name, "country_id": country_id, "city_id": city_id,"state_id":state_id}
            logger.info(f"Getting city id completed")
            return data
        except Exception as e:
            logger.error(f"Error getting city id: {e}")
            raise e