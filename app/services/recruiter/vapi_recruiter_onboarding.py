import logging
from services.external.openai import OpenAIServiceClass
from hashids import Hashids
import random
import time
from dependencies import HASHID_SALT,OPENSEARCH_RECRUITER_REQUISITION_INDEX,UNIVERSAL_PASSWORD
import json
from celery_tasks.post_requisition_process_tasks import insert_requisition_details_to_opensearch
from services.external.opensearch import OpenSearchServiceClass
from services.user.user import UserServiceClass
from services.organization.organization import OrganizationServiceClass
from models.professions import Professions
from models.recruiter import Recruiter
from celery_tasks.recruiter_conversation import create_recruiter_requisition
from schemas.recruiter import RecruiterCreateByVapi
logger = logging.getLogger(__name__)

open_ai_service_object = OpenAIServiceClass()
user_service_object = UserServiceClass()
organization_service_object = OrganizationServiceClass()

class RecruiterOnboardingServiceClass:

    def insert_recruiter_details_into_open_search(self, transcript, tool_calls):
        try:
            logger.info("Recruiter details insertion started")

            # Directly access the first tool call in the first message, if it exists
            if tool_calls and "tool_calls" in tool_calls[0]:
                tool_call = tool_calls[0]["tool_calls"][0]  # Direct access to the single tool call

                try:
                    function_args = tool_call.get("function", {}).get("arguments", "")
                    arguments_dict = json.loads(function_args)
                    destination = arguments_dict.get("destination", "")
                    # Check if the destination is "Recruiter Responder"
                    if destination == "Recruiter Responder":
                        logger.info("Recruiter Responder detected. Ending the loop.")
                        return {"status": 0}
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to decode arguments from tool_calls: {e}")
            # If no "Recruiter Responder" found, proceed with parsing
            logger.info("JD assistant message detected. Parsing recruiter info.")
            response = open_ai_service_object.parse_recruiter_requisition(transcript)
            salt = HASHID_SALT
            random_number = random.randint(1, 1000)
            timestamp = int(time.time())
            hashids = Hashids(salt=salt)
            generated_hash = hashids.encode(random_number, timestamp)
            open_search_data = {
            "job_title": response.get("job_title", ""),
            "recruiter_first_name": "",
            "recruiter_last_name": "",
            "recruiter_email_address": "",
            "recruiter_company_name": "",
            "recruiter_contact":"",
            "status":0,
            "hash": generated_hash,
            "must_have_technical_skills": response.get("must_have_technical_skills", []),
            "required_experience_years": response.get("required_experience_years", ""),
            "specific_tools_technologies_certifications": response.get("specific_tools_technologies_certifications", []),
            "location_requirements": response.get("location_requirements", ""),
            "onsite_or_hybrid_details": response.get("onsite_or_hybrid_details", {}),
            "fully_remote_details": response.get("fully_remote_details", {}),
            "organizational_context": response.get("organizational_context", {}),
            "negative_constraints": response.get("negative_constraints", []),
            "role_pitch": response.get("role_pitch", ""),
            "salary_details": response.get("salary_details", {}),
            "company_description": response.get("company_description", ""),
            "preferred_previous_positions": response.get("preferred_previous_positions", []),
            "preferred_industries": response.get("preferred_industries", []),
            "transcript":transcript
        }
            logger.info("Inserting recruiter details into OpenSearch")
            insert_requisition_details_to_opensearch.delay(document_id=generated_hash, data=open_search_data)

            return {
                "status": 1,
                "parsed_response": response,
                "hash": generated_hash,
            }

        except Exception as e:
            logger.error(f"Error inserting recruiter details: {e}")
            raise e


    def update_recruiter_details_into_open_search(self, document_id, updated_data,recruiter_info,db):
        try:

            logger.info("Recruiter requisition details update started")

            logger.info(f"Updated data: {updated_data}")

            open_search_service_object = OpenSearchServiceClass(OPENSEARCH_RECRUITER_REQUISITION_INDEX)
            response = open_search_service_object.update_req_details_in_opensearch(document_id=document_id, data=updated_data)
            mapped_data=self.map_recruiter_requisition_details(updated_data)
            recruiter_data={
                "first_name":recruiter_info.get("recruiter_first_name",""),
                "last_name":recruiter_info.get("recruiter_last_name",""),
                "email":recruiter_info.get("recruiter_email_address",""),
                "phone":recruiter_info.get("recruiter_contact",""),
                "password":UNIVERSAL_PASSWORD,
                "company_name":recruiter_info.get("recruiter_company_name",""),
                "city_id":recruiter_info.get("recruiter_city_id"),
                "country_id":recruiter_info.get("recruiter_country_id"),
                "state_id":recruiter_info.get("recruiter_state_id"),
            }
            register_recruiter_data=self.register_recruiter(recruiter_data,db)
            recruiter=register_recruiter_data.get("recruiter")
            is_recruiter_exists=register_recruiter_data.get("is_recruiter_exists")


            profession_id=self.get_profession_id(updated_data.get("job_title",""),db)
            create_recruiter_requisition.delay(recruiter.organization_id,document_id,recruiter.id,profession_id,mapped_data,recruiter_data,is_recruiter_exists)


            logger.info("Recruiter requisition details updated successfully")
            return True

        except Exception as e:
            logger.error(f"Error updating recruiter details: {e}")
            raise e

    def update_recruiter_info_into_open_search(self, document_id, recruiter_info):
        try:
            logger.info("Recruiter info update started")
            open_search_service_object = OpenSearchServiceClass(OPENSEARCH_RECRUITER_REQUISITION_INDEX)
            response = open_search_service_object.update_req_info_in_opensearch(document_id=document_id, recruiter_info=recruiter_info)
            logger.info("Recruiter info updated successfully")
            return True
        except Exception as e:
            logger.error(f"Error updating recruiter info: {e}")
            raise e
    def map_recruiter_requisition_details(self, parsed_response):
        try:
            logger.info("Mapping recruiter requisition details started")
            response = open_ai_service_object.map_recruiter_requisition(parsed_response)
            return response
        except Exception as e:
            logger.error(f"Error mapping recruiter requisition: {e}")
            raise e

    def get_profession_id(self,job_title,db):
        try:
            logger.info("Getting profession ID started")
            profession_names = db.query(Professions.name).all()
            profession_info=open_ai_service_object.get_profession(profession_names,job_title)
            profession_name = profession_info.get("name", "")

            profession = db.query(Professions).filter(Professions.name == profession_name).first()
            logger.info("Profession ID retrieved successfully")
            return profession.id if profession else None

        except Exception as e:
            logger.error(f"Error getting profession ID: {e}")
            raise e
    def register_recruiter(self,recruiter_data,db):
        try:
            logger.info("Registering recruiter started")
            logger.info(f"Recruiter data: {recruiter_data}")
            recruiter_email=recruiter_data.get("email","")

            logger.info(f"Recruiter email: {recruiter_email}")
            existing_user = db.query(Recruiter).filter(
                Recruiter.email == recruiter_email).first()

            if existing_user:
                logger.info("Recruiter already exists")
                data={
                    "recruiter":existing_user,
                    "is_recruiter_exists":True
                }
                return data
            recruiter_create_data=RecruiterCreateByVapi(
                first_name=recruiter_data.get("first_name",""),
                last_name=recruiter_data.get("last_name",""),
                email=recruiter_email,
                phone=recruiter_data.get("phone",""),
                password=UNIVERSAL_PASSWORD,

            )
            user=user_service_object.register_recruiter_user(recruiter_create_data,db)
            organization_input = {
                "name": recruiter_data.get("company_name",""),
                "city_id":recruiter_data.get("city_id"),
                "logo":None,
            }
            organization = organization_service_object.add_organization(organization_input, db)
            logger.info(f"Organization: {organization}")
            logger.info(f"Organization id:{organization.id}")
            logger.info(f"User id: {user.id}")
            update_user = (
                db.query(Recruiter)
                .filter(Recruiter.id == user.id)
                .update({"organization_id": organization.id})
            )
            db.commit()
            logger.info("Recruiter registered successfully")
            data={
                "recruiter": user,
                "is_recruiter_exists": False
            }
            return data
        except Exception as e:
            logger.error(f"Error registering recruiter: {e}")
            raise e