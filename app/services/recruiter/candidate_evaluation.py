from models.interview_feedback import InterviewFeedback
from models.interview import Interview
from models.user import User
from models.city import City
from sqlalchemy.orm import  joinedload
from custom_exceptions import  EntityNotFoundException
from models.shortlisted_candidate import ShortlistedCandidate
from fastapi.encoders import jsonable_encoder
from models.question_feedback import QuestionFeedback
from models.user_education import UserEducation
from models.user_experience import UserExperience
from models.requisition import Requisition
from models.recommended_candidates import RecommendedCandidates
from models.user_requisition import UserRequisition
from models.country import Country
from models.user_skill import UserSkill
from services.requisition.requisition import RequisitionServiceClass
from services.filemanager.filemanager import FileManagerClass
from dependencies import AWS_VIDEO_FOLDER, CANDIDATE_THRESHOLD_SCORE,RECOMMENDED_CANDIDATE_THRESHOLD_SCORE, CONTACT_US_EMAIL
from services.notification.email import send_email_background
import math
import json
from sqlalchemy import func, or_
from helper.helper import get_shortlisted_candidates_ids
from schemas.interview_feedback import CandidateFilterRequest, CandidateFilterRequest
from datetime import datetime
from helper.interview_helper import paginate_results, apply_interview_filters

class CandidateEvaluationServiceClass:
    def __init__(self):
        pass

    def get_suitable_candidates(self, requisition_id, request, db):
        try:
            page = int(request.query_params.get("page", 1))
            per_page = int(request.query_params.get("per_page", 10))

            country_id = request.query_params.get("country_id")
            country_id = int(country_id) if country_id and country_id.isdigit() else None

            city_id = request.query_params.get("city_id")
            city_id = int(city_id) if city_id and city_id.isdigit() else None

            shorlisted_candidates = get_shortlisted_candidates_ids(
                requisition_id, db
            )

            query = (
                db.query(InterviewFeedback)
                .join(InterviewFeedback.user)  # Join with the User model to filter on User.city_id
                .options(joinedload(InterviewFeedback.user)
                         )  # Optimize loading of the user and city relationships
                .filter(
                    InterviewFeedback.requisition_id == requisition_id,
                    InterviewFeedback.score >= CANDIDATE_THRESHOLD_SCORE,
                    InterviewFeedback.status == InterviewFeedback.STATUS_APPROVED,
                    InterviewFeedback.user_id.notin_(shorlisted_candidates),
                )
                .order_by(InterviewFeedback.score.desc(), InterviewFeedback.id.asc())
            )

            if country_id is None:
                query = query.filter(InterviewFeedback.satisfies_binary_requirements == True)
            else:
                if country_id is not None and city_id is None:
                    city_ids = [city.id for city in db.query(City).filter(City.country_id == country_id).all()]
                    query = query.filter(User.city_id.in_(city_ids))
                else:
                    query = query.filter(User.city_id == city_id)

            total = query.count()
            interview_feedbacks = (
                query.offset((page - 1) * per_page)
                .limit(per_page)
                .all()
            )

            for interview_feedback in interview_feedbacks:
                # No need for the following code as it is already done in the model
                # interview_feedback.user_video_links = self.get_candidate_videos_from_s3(
                #     interview_feedback.user_id, interview_feedback.interview_id, db
                # )
                interview_feedback.questions = (
                    db.query(QuestionFeedback)
                    .filter(
                        QuestionFeedback.interview_id == interview_feedback.interview_id
                    )
                    .order_by(QuestionFeedback.id.asc())
                    .all()
                )

                interview_feedback.feedback = json.loads(interview_feedback.feedback)
                interview_feedback.shortlisted_candidate = (
                    db.query(ShortlistedCandidate)
                    .filter(
                        ShortlistedCandidate.candidate_id == interview_feedback.user_id,
                        ShortlistedCandidate.requisition_id == requisition_id,
                    )
                    .first()
                )
            return {
                "pagination": {
                    "total": total,
                    "last_page": math.ceil(total / per_page),
                    "page": page,
                    "per_page": per_page,
                },
                "items": jsonable_encoder(interview_feedbacks),
            }

        except Exception as e:
            raise e

    def get_candidate(self, interview_feedback_id, db):
        try:
            interview_feedback = (
                db.query(InterviewFeedback)
                .options(
                    joinedload(InterviewFeedback.user)
                    .joinedload(User.educations),
                    joinedload(InterviewFeedback.user)
                    .joinedload(User.experiences),
                    joinedload(InterviewFeedback.user),
                    joinedload(InterviewFeedback.user)
                    .joinedload(User.educations)
                    .joinedload(UserEducation.school),
                    joinedload(InterviewFeedback.user)
                    .joinedload(User.experiences)
                    .joinedload(UserExperience.organization),
                    joinedload(InterviewFeedback.user)
                    .joinedload(User.skills).joinedload(UserSkill.skill)
                )
                .filter(
                    InterviewFeedback.id == interview_feedback_id,
                    # InterviewFeedback.status == InterviewFeedback.STATUS_APPROVED,
                )
                .first()
            )
            questions_feedback = (
                db.query(QuestionFeedback)
                .filter(
                    QuestionFeedback.interview_id == interview_feedback.interview_id
                )
                .order_by(QuestionFeedback.id.asc())
                .all()
            )

            interview_feedback.questions = questions_feedback

            # interview_feedback.user_video_links = self.get_candidate_videos_from_s3(
            #     interview_feedback.user_id, interview_feedback.interview_id, db
            # )

            interview_feedback.feedback = json.loads(interview_feedback.feedback)
            interview_feedback.shortlisted_candidate = (
                db.query(ShortlistedCandidate)
                .filter(
                    ShortlistedCandidate.candidate_id == interview_feedback.user_id,
                    ShortlistedCandidate.requisition_id == interview_feedback.requisition_id,
                )
                .first()
            )
            return jsonable_encoder(interview_feedback)
        except Exception as e:
            raise e

    def take_action_on_profile(
        self, interview_feedback_input, request, background_tasks, db
    ):
        try:
            candidate_type = request.query_params.get("type", "interviewed_candidates")
            
            # Handle different candidate types
            if candidate_type == "recommended":
                entity = (
                    db.query(RecommendedCandidates)
                    .filter(
                        RecommendedCandidates.id
                        == interview_feedback_input.interview_feedback_id
                    )
                    .first()
                )
            elif candidate_type == "non-interviewed-candidates":
                # For non-interviewed candidates, we don't need to query for an entity
                # as interview_feedback_id will be 0
                entity = None
            else:  # default: interviewed_candidates
                entity = (
                    db.query(InterviewFeedback)
                    .filter(
                        InterviewFeedback.id
                        == interview_feedback_input.interview_feedback_id
                    )
                    .first()
                )

            # For non-interviewed candidates, we don't validate entity existence
            if candidate_type != "non-interviewed-candidates" and not entity:
                raise EntityNotFoundException("Invalid Entity")
                
            # Get or create shortlisted candidate
            if candidate_type == "non-interviewed-candidates":
                # For non-interviewed candidates, directly check if candidate already shortlisted
                shorlisted_candidate = (
                    db.query(ShortlistedCandidate)
                    .filter(
                        ShortlistedCandidate.candidate_id == interview_feedback_input.user_id,
                        ShortlistedCandidate.requisition_id == 0
                    )
                    .first()
                )
                
                if not shorlisted_candidate:
                    # Create new shortlisted candidate with interview_id=0
                    shorlisted_candidate = ShortlistedCandidate(
                        interview_id=0,  # Zero for non-interviewed candidates
                        requisition_id=0,
                        candidate_id=interview_feedback_input.user_id,
                        recruiter_id=request.state.user.id,
                        status=interview_feedback_input.status,
                        feedback=interview_feedback_input.feedback,
                    )
                    db.add(shorlisted_candidate)
                else:
                    shorlisted_candidate.status = interview_feedback_input.status
                    shorlisted_candidate.feedback = interview_feedback_input.feedback
                
            else:
                # Original logic for interviewed and recommended candidates
                shorlisted_candidate = (
                    db.query(ShortlistedCandidate)
                    .filter(
                        ShortlistedCandidate.interview_id == entity.interview_id,
                        ShortlistedCandidate.candidate_id == entity.user_id,
                    )
                    .first()
                )
                if not shorlisted_candidate:
                    shorlisted_candidate = ShortlistedCandidate(
                        interview_id=entity.interview_id,
                        requisition_id=entity.requisition_id,
                        candidate_id=entity.user_id,
                        recruiter_id=request.state.user.id,
                        status=interview_feedback_input.status,
                        feedback=interview_feedback_input.feedback,
                    )
                    db.add(shorlisted_candidate)
                else:
                    shorlisted_candidate.status = interview_feedback_input.status
                    shorlisted_candidate.feedback = interview_feedback_input.feedback
            
            db.commit()
            db.refresh(shorlisted_candidate)

            # send email on shortlist candidate
            # if (
            #     interview_feedback_input.status
            #     == ShortlistedCandidate.STATUS_INTERVIEW_SCHEDULED
            # ):
            #     self.__send_email_to_team(
            #         request, shorlisted_candidate, background_tasks, db
            #     )
            if candidate_type != 'non-interviewed-candidates':
                self.__send_email_to_team(
                    request,
                    shorlisted_candidate,
                    background_tasks,
                    db,
                    interview_feedback_input.status,
                )

            return shorlisted_candidate
        except Exception as e:
            raise e

    def get_candidate_videos_from_s3(self, user_id, interview_id, db):
        try:
            # interview_feedback = (
            #     db.query(InterviewFeedback)
            #     .filter(InterviewFeedback.id == interview_feedback_id)
            #     .first()
            # )
            video_links = FileManagerClass().get_s3_contents(
                AWS_VIDEO_FOLDER + str(user_id) + "/" + str(interview_id) + "/"
            )
            return video_links
        except Exception as e:
            raise e

    def get_shortlisted_candidates(self, request, db):
        try:
            page = int(request.query_params.get("page", 1))
            per_page = int(request.query_params.get("per_page", 10))
            requisition_id = request.query_params.get("requisition_id", None)
            status = request.query_params.get("status")
            q = request.query_params.get("q", None)
            most_recent = request.query_params.get("most_recent", False)
            city_id = request.query_params.get("city_id", None)


            if not requisition_id:
                requsition_ids_of_current_recruiter = (
                    self.__get_requsition_ids_of_current_recruiter(request, db)
                )
            else:
                requsition_ids_of_current_recruiter = [requisition_id]

            query = (
                db.query(ShortlistedCandidate)
                .options(
                    joinedload(ShortlistedCandidate.user),
                    joinedload(ShortlistedCandidate.requisition),
                )
                .filter(
                    ShortlistedCandidate.requisition_id.in_(
                        requsition_ids_of_current_recruiter
                    ),
                    ShortlistedCandidate.recruiter_id == request.state.user.id,
                    ShortlistedCandidate.status == status
                )
            )
            if most_recent:
                query = query.order_by(ShortlistedCandidate.id.desc())
            if city_id:
                query = query.join(ShortlistedCandidate.user).filter(User.city_id == city_id ) #need to test it
            if q:
                query= query.join(ShortlistedCandidate.user).filter(or_(
                    User.first_name.ilike(f"%{q}%"),
                    User.email.ilike(f"%{q}%")
                ))

            total = query.count()
            shortlisted_candidates = (
                query.offset((page - 1) * per_page).limit(per_page).all()
            )

            for shortlisted_candidate in shortlisted_candidates:

                shortlisted_candidate.questions = (
                    db.query(QuestionFeedback)
                    .filter(
                        QuestionFeedback.interview_id
                        == shortlisted_candidate.interview_id
                    )
                    .order_by(QuestionFeedback.id.asc())
                    .all()
                )

                check_for_interview = (
                    db.query(Interview)
                    .filter(
                        Interview.requisition_id
                        == shortlisted_candidate.requisition_id,
                        Interview.user_id
                        == shortlisted_candidate.candidate_id,
                    )
                    .first()
                )
                shortlisted_candidate.is_recommended_candidate = (
                    False if check_for_interview else True
                )
                if check_for_interview:
                    interview_feedback = (
                        db.query(InterviewFeedback)
                        .filter(
                            InterviewFeedback.requisition_id
                            == shortlisted_candidate.requisition_id,
                            InterviewFeedback.user_id
                            == shortlisted_candidate.candidate_id,
                        )
                        .first()
                    )
                    shortlisted_candidate.feedback_id = interview_feedback.id
                    parsed_feedback = json.loads(interview_feedback.feedback)
                    shortlisted_candidate.skills = parsed_feedback.get('skills', []) 
                    
                else:
                    shortlisted_candidate.feedback_id = (
                        db.query(RecommendedCandidates)
                        .filter(
                            RecommendedCandidates.requisition_id
                            == shortlisted_candidate.requisition_id,
                            RecommendedCandidates.user_id
                            == shortlisted_candidate.candidate_id,
                        )
                        .first()
                    )
                    shortlisted_candidate.feedback_id = interview_feedback.id
                    parsed_feedback = json.loads(interview_feedback.feedback)
                    shortlisted_candidate.skills = parsed_feedback.get('skills', [])

            return {
                "pagination": {
                    "total": total,
                    "last_page": math.ceil(total / per_page),
                    "page": page,
                    "per_page": per_page,
                },
                "items": jsonable_encoder(shortlisted_candidates),
            }
        except Exception as e:
            raise e

    def __get_requsition_ids_of_current_recruiter(self, request, db):
        try:
            requisition_ids = (
                db.query(Requisition.id)
                .filter(
                    Requisition.organization_id == request.state.user.organization_id
                )
                .all()
            )
            requisition_ids = [req_id[0] for req_id in requisition_ids]
            return requisition_ids
        except Exception as e:
            raise e

    def action_on_shortlisted_candidate(self, shortlisted_candidate_id, payload, db):
        try:
            shortlisted_candidate = (
                db.query(ShortlistedCandidate)
                .filter(ShortlistedCandidate.id == shortlisted_candidate_id)
                .first()
            )
            if not shortlisted_candidate:
                raise EntityNotFoundException("Shortlisted candidate not found")

            shortlisted_candidate.status = payload.status
            db.commit()
            return True
        except Exception as e:
            raise e

    def get_recommended_candidates(self, requisition_id, request, db, payload = None):
        try:
            if payload is not None:
                page = payload.page
                per_page = payload.per_page
                search_query = payload.q
                location = payload.location
                most_recent = payload.most_recent
                country_id = payload.country_id
                city_id = payload.city_id

            else:
                page = int(request.query_params.get("page", 1)) 
                per_page = int(request.query_params.get("per_page", 10))

                country_id = request.query_params.get("country_id")
                country_id = int(country_id) if country_id and country_id.isdigit() else None

                city_id = request.query_params.get("city_id") 
                city_id = int(city_id) if city_id and city_id.isdigit() else None

            shorlisted_candidates = get_shortlisted_candidates_ids(
                requisition_id, db
            )

            query = (
                db.query(RecommendedCandidates)
                .join(RecommendedCandidates.user)
                .options(joinedload(RecommendedCandidates.user))
                .filter(
                    RecommendedCandidates.requisition_id == requisition_id,
                    RecommendedCandidates.score >= RECOMMENDED_CANDIDATE_THRESHOLD_SCORE,
                    RecommendedCandidates.user_id.notin_(shorlisted_candidates),
                )
                .order_by(
                    RecommendedCandidates.score.desc(), RecommendedCandidates.id.asc()
                )
            )

            if city_id is not None:
                query = query.filter(User.city_id == city_id)
            elif country_id is not None:
                city_ids = [city.id for city in db.query(City.id).filter(City.country_id == country_id).all()]
                if city_ids:
                    query = query.filter(User.city_id.in_(city_ids))

            if most_recent:
                query = query.order_by(RecommendedCandidates.id.desc())

            if search_query:
                query = query.join(RecommendedCandidates.user).filter(
                    or_(
                        User.first_name.ilike(f"%{search_query}%"),
                        User.email.ilike(f"%{search_query}%")
                    )
                )

            total = query.count()

            recommended_candidates = (
                query.order_by(RecommendedCandidates.score.desc())
                .offset((page - 1) * per_page)
                .limit(per_page)
                .all()
            )

            for recommended_candidate in recommended_candidates:
                # recommended_candidate.user_video_links = (
                #     self.get_candidate_videos_from_s3(
                #         recommended_candidate.user_id,
                #         recommended_candidate.interview_id,
                #         db,
                #     )
                # )
                recommended_candidate.questions = (
                    db.query(QuestionFeedback)
                    .filter(
                        QuestionFeedback.interview_id == recommended_candidate.interview_id
                    )
                    .order_by(QuestionFeedback.id.asc())
                    .all()
                )
                recommended_candidate.shortlisted_candidate = (
                    db.query(ShortlistedCandidate)
                    .filter(
                        ShortlistedCandidate.candidate_id
                        == recommended_candidate.user_id,
                        ShortlistedCandidate.requisition_id == requisition_id,
                    )
                    .first()
                )
            return {
                "pagination": {
                    "total": total,
                    "last_page": math.ceil(total / per_page),
                    "page": page,
                    "per_page": per_page,
                },
                "items": jsonable_encoder(recommended_candidates),
            }

        except Exception as e:
            raise e

    def get_recommended_candidate(self, recommended_candidate_id, db):
        try:
            recommended_candidate = (
                db.query(RecommendedCandidates)
                .options(
                    joinedload(RecommendedCandidates.user)
                    .joinedload(User.educations),
                    joinedload(RecommendedCandidates.user)
                    .joinedload(User.experiences),
                    joinedload(RecommendedCandidates.user),
                    joinedload(RecommendedCandidates.user)
                    .joinedload(User.educations)
                    .joinedload(UserEducation.school),
                    joinedload(RecommendedCandidates.user)
                    .joinedload(User.experiences)
                    .joinedload(UserExperience.organization),
                )
                .filter(
                    RecommendedCandidates.id == recommended_candidate_id,
                )
                .first()
            )
            questions_feedback = (
                db.query(QuestionFeedback)
                .filter(
                    QuestionFeedback.interview_id == recommended_candidate.interview_id
                )
                .order_by(QuestionFeedback.id.asc())
                .all()
            )

            recommended_candidate.questions = questions_feedback

            # This is temporary fix to handle the feedback field
            try:
                recommended_candidate.feedback = json.loads(recommended_candidate.feedback)
            except (TypeError, json.JSONDecodeError):
                recommended_candidate.feedback = recommended_candidate.feedback

            recommended_candidate.shortlisted_candidate = (
                db.query(ShortlistedCandidate)
                .filter(
                    ShortlistedCandidate.candidate_id == recommended_candidate.user_id,
                    ShortlistedCandidate.requisition_id == recommended_candidate.requisition_id,
                )
                .first()
            )
            # recommended_candidate.feedback = json.loads(
            #     interview_feedback.feedback)
            return jsonable_encoder(recommended_candidate)
        except Exception as e:
            raise e

    def __send_email_to_team(self, request, shorlisted_candidate, background_tasks, db, status):
        try:
            type = ''
            
            requisition = RequisitionServiceClass().get_requisition(
                request, shorlisted_candidate.requisition_id, db
            )
            
            if status == ShortlistedCandidate.STATUS_INTERVIEW_SCHEDULED:
                type = "Shortlisted"
                subject = f"Candidate {type} – Schedule Interview with {str(request.state.user.first_name + ' ' + request.state.user.last_name)}"
                content = f"We’re excited to inform you that a candidate has been shortlisted for the <strong>{requisition['title']}</strong> role at <strong>{requisition['organization']['name']}</strong>. Please arrange the interview between the shortlisted candidate and the recruiter, from {requisition['organization']['name']}, at your earliest convenience."
            elif status == ShortlistedCandidate.STATUS_REJECTED:
                type = "Rejected"
                subject = f"Candidate {type} by {str(request.state.user.first_name + ' ' + request.state.user.last_name)}"
                content = f"The following candidate has been rejected for the <strong>{requisition['title']}</strong> role by the recruiter from <strong>{requisition['organization']['name']}</strong>"
            
            elif status == ShortlistedCandidate.STATUS_SAVED_PIPELINE:
                type = "Saved to Pipeline"
                subject = f"Candidate {type} by {str(request.state.user.first_name + ' ' + request.state.user.last_name)}"
                content = f"The following candidate has been saved in the pipeline to be later considered for the interview for <strong>{requisition['title']}</strong> role by the recruiter from <strong>{requisition['organization']['name']}</strong>"
                
            elif status == ShortlistedCandidate.STATUS_SEARCHED_SAVED_PIPELINE:
                type = "Searched Saved to Pipeline"
                subject = f"Candidate {type} by {str(request.state.user.first_name + ' ' + request.state.user.last_name)}"
                content = f"The following candidate has been saved in the pipeline to be later considered for the interview for <strong>{requisition['title']}</strong> role by the recruiter from <strong>{requisition['organization']['name']}</strong>"
                
            elif status == ShortlistedCandidate.STATUS_SEARCHED_SHORTLISTED:
                type = "Searched Shortlisted"
                subject = f"Candidate {type} by {str(request.state.user.first_name + ' ' + request.state.user.last_name)}"
                content = f"We’re excited to inform you that a candidate has been shortlisted for the <strong>{requisition['title']}</strong> role at <strong>{requisition['organization']['name']}</strong>. Please arrange the interview between the shortlisted candidate and the recruiter, from {requisition['organization']['name']}, at your earliest convenience."


            candidate = (
                db.query(User)
                .filter(User.id == shorlisted_candidate.candidate_id)
                .first()
            )
            email_body = {
                "name": f"{request.state.user.first_name} {request.state.user.last_name}",
                "job_title": requisition["title"],
                "company": requisition["organization"]["name"],
                "candidate": jsonable_encoder(candidate),
                "type": type.lower(),
                "recruiter": jsonable_encoder(request.state.user),
                "content": content,
            }
            # send email
            send_email_background(
                background_tasks,
                subject,
                CONTACT_US_EMAIL,
                email_body,
                "candidate_action_for_team.html",
            )
        except Exception as e:
            raise e    


    def get_all_candidates(self, requisition_id, payload, db): #get all interviewed and recommended candidates
        try:
            page = payload.page
            per_page = payload.per_page
            search_query = payload.q
            location = payload.location
            most_recent = payload.most_recent

            shortlisted_candidate_ids = get_shortlisted_candidates_ids(
                requisition_id, db
            )

            # Query for recommended candidates
            recommended_query = (
                db.query(RecommendedCandidates)
                .join(RecommendedCandidates.user)
                .options(joinedload(RecommendedCandidates.user))
                .filter(
                    RecommendedCandidates.requisition_id == requisition_id,
                    RecommendedCandidates.score >= RECOMMENDED_CANDIDATE_THRESHOLD_SCORE,
                    RecommendedCandidates.user_id.notin_(shortlisted_candidate_ids),
                )
                .order_by(
                    RecommendedCandidates.score.desc(), RecommendedCandidates.id.asc()
                )
            )

            # Query for interviewed candidates
            interviewed_query = (
                db.query(InterviewFeedback)
                .join(InterviewFeedback.user)
                .options(joinedload(InterviewFeedback.user))
                .filter(
                    InterviewFeedback.requisition_id == requisition_id,
                    InterviewFeedback.score >= CANDIDATE_THRESHOLD_SCORE,
                    # InterviewFeedback.status == InterviewFeedback.STATUS_APPROVED,
                    InterviewFeedback.user_id.notin_(shortlisted_candidate_ids),
                )
                .order_by(InterviewFeedback.score.desc(), InterviewFeedback.id.asc())
            )

            # Get results from both queries
            recommended_candidates = recommended_query.all()
            interviewed_candidates = interviewed_query.all()

            # Collect all interview IDs to fetch question feedback in bulk
            interview_ids = [candidate.id for candidate in interviewed_candidates if hasattr(candidate, 'id')]
            
            # Bulk fetch question feedback for all interviews
            questions_feedback_dict = {}
            if interview_ids:
                all_questions_feedback = (
                    db.query(QuestionFeedback)
                    .filter(
                        QuestionFeedback.interview_id.in_(interview_ids),
                        QuestionFeedback.statemachine_node.notin_(["closure", "inquiry", "answer-query"])
                    )
                    .order_by(QuestionFeedback.id.asc())
                    .all()
                )
                
                # Group question feedback by interview_id
                for question in all_questions_feedback:
                    if question.interview_id not in questions_feedback_dict:
                        questions_feedback_dict[question.interview_id] = []
                    questions_feedback_dict[question.interview_id].append(question)

            # Combine results and add 'type' field
            combined_candidates = []
            
            for candidate in recommended_candidates:
                candidate_dict = jsonable_encoder(candidate)
                candidate_dict["type"] = "recommended"
                combined_candidates.append(candidate_dict)
                
            for candidate in interviewed_candidates:
                # Parse feedback JSON
                if hasattr(candidate, 'feedback') and candidate.feedback:
                    try:
                        candidate.feedback = json.loads(candidate.feedback)
                    except (json.JSONDecodeError, TypeError):
                        # Keep as is if not valid JSON
                        pass
                
                # Add questions feedback
                if hasattr(candidate, 'interview_id') and candidate.interview_id in questions_feedback_dict:
                    candidate.questions = questions_feedback_dict[candidate.interview_id]
                else:
                    candidate.questions = []
                
                candidate_dict = jsonable_encoder(candidate)
                candidate_dict["type"] = "interviewed"
                combined_candidates.append(candidate_dict)
                
            # Sort combined results by score (descending)
            combined_candidates.sort(key=lambda x: (x.get("score", 0), x.get("id", 0)), reverse=True)
            
            # Apply pagination to combined results
            total = len(combined_candidates)
            start_idx = (page - 1) * per_page
            end_idx = start_idx + per_page
            paginated_candidates = combined_candidates[start_idx:end_idx]
        
            return paginated_candidates

        except Exception as e:
            raise e

    def get_applied_candidates(self, requisition_id, payload, db):
        try:
            search_query = payload.q
            country_id = payload.country_id
            city_id = payload.city_id
            most_recent = payload.most_recent

            # Query for applied candidates
            query = (
                db.query(UserRequisition)
                .options(joinedload(UserRequisition.user))
                .filter(
                    UserRequisition.requisition_id == requisition_id,
                    UserRequisition.is_applied == 1,
                )
            )

            # Add search filter using join
            if search_query:
                query = query.join(UserRequisition.user).filter(
                    or_(
                        User.first_name.ilike(f"%{search_query}%"),
                        User.email.ilike(f"%{search_query}%")
                    )
                )

            # Add ordering
            if most_recent:
                query = query.join(UserRequisition.user).order_by(User.id.desc())

            # Add location filter using join
            if country_id:
                query = query.join(UserRequisition.user).filter(
                        User.country_id == country_id
                )

            if city_id:
                query = query.join(UserRequisition.user).filter(
                        User.city_id == city_id
                )


            applied_candidates = query.all()

            return jsonable_encoder(applied_candidates)
        except Exception as e:
            raise e    
    def get_pending_interview_scheduled_candidates(self, requisition_id, payload, db):
        try:
            search_query = payload.q
            # country_id = payload.country_id
            # city_id = payload.city_id
            location = payload.location
            most_recent = payload.most_recent

            requisition_interview_candidates = db.query(Interview).filter(
                Interview.requisition_id == requisition_id,
                Interview.status == Interview.INTERVIEW_PENDING, #confirm about the statusss
            )

            requisition_interview_candidate_ids = [interview.user_id for interview in requisition_interview_candidates]

            query = (
                db.query(UserRequisition)
                .options(joinedload(UserRequisition.user))
                .filter(
                    UserRequisition.requisition_id == requisition_id,
                    UserRequisition.is_applied == 1,
                    UserRequisition.user_id.not_in(requisition_interview_candidate_ids)
                )
            )

            # Add search filter using join
            if search_query:
                query = query.join(UserRequisition.user).filter(
                    or_(
                        User.first_name.ilike(f"%{search_query}%"),
                        User.email.ilike(f"%{search_query}%")
                    )
                )

            # Add ordering
            if most_recent:
                query = query.join(UserRequisition.user).order_by(User.id.desc())

            # Add location filter using join
            if location:
                query = query.join(UserRequisition.user).filter(
                    or_(
                        User.city_id == location,
                        User.country_id == location
                    )
                )

            applied_candidates = query.all()

            return jsonable_encoder(applied_candidates)
        except Exception as e:
            raise e
        
    def get_rejected_candidates(self, requisition_id, payload, db):
        try:
            search_query = payload.q
            country_id = payload.country_id
            city_id = payload.city_id
            location = payload.location
            most_recent = payload.most_recent
            # Query for rejected candidates
            query = (
                db.query(ShortlistedCandidate)
                .options(joinedload(ShortlistedCandidate.user))                
                .filter(
                    ShortlistedCandidate.requisition_id == requisition_id,
                    ShortlistedCandidate.status == ShortlistedCandidate.STATUS_REJECTED,
                )
            )
            # Add search filter using join
            if search_query:
                query = query.join(ShortlistedCandidate.user).filter(
                    or_(
                        User.first_name.ilike(f"%{search_query}%"),
                        User.email.ilike(f"%{search_query}%")
                    )
                )

            # Add ordering
            if most_recent:
                query = query.join(ShortlistedCandidate.user).order_by(User.id.desc())

            # Add location filter using join
            if location:
                query = query.join(ShortlistedCandidate.user).filter(
                    or_(
                        User.city_id == location,
                        User.country_id == location
                    )
                )
            if country_id:
                query = query.join(ShortlistedCandidate.user).filter(
                    User.country_id == country_id
                )
            if city_id:
                query = query.join(ShortlistedCandidate.user).filter(
                    User.city_id == city_id
                )

            rejected_candidates = query.all()

            return jsonable_encoder(rejected_candidates)
        except Exception as e:
            raise e

    def scheduleRequisitionCandidateInterview(self, payload: CandidateFilterRequest, db):
        try:
            requisition_id = payload.requisition_id
            page = payload.page
            per_page = payload.per_page
            filter_types = getattr(payload, 'filters', [])

            search_query = payload.q
            city_id = payload.city_id
            most_recent = payload.most_recent

            query = db.query(User).join(Interview.user).filter(
                Interview.requisition_id == requisition_id,
            )
            if search_query:
                query = query.filter(or_(
                    User.first_name.ilike(f"%{search_query}%"),
                    User.email.ilike(f"%{search_query}%")
                ))
            if filter_types:
                query = apply_interview_filters(query, filter_types)
            if most_recent:
                query = query.order_by(User.id.desc())
            if city_id:
                query = query.filter(User.city_id == city_id)

            return paginate_results(query, page, per_page) 

        except Exception as e:
            raise e
    def get_long_list_candidates(self, request, payload: CandidateFilterRequest, db): 
        #add counts, viewed, featured-- from user_Activity

        try:
            requisition_id = payload.requisition_id
            page = payload.page
            per_page = payload.per_page
            filter_types = getattr(payload, 'filters', ['all'])

            if not filter_types:   # Default to ['all'] if not specified
                filter_types = ['all']
                
            # Initialize an empty list to store all candidates
            all_candidates = []
            
            if 'all' in filter_types:
                all_candidates = self.get_all_candidates(requisition_id, payload, db)
            else:
                # Process each filter type and combine results
                for filter_type in filter_types:
                    if filter_type == 'applied_all':
                        candidates = self.get_applied_candidates(requisition_id, payload, db)
                        all_candidates.extend(candidates)
                    elif filter_type == 'pending':
                        candidates = self.get_pending_interview_scheduled_candidates(requisition_id, payload, db)
                        all_candidates.extend(candidates)
                    elif filter_type == 'featured':
                        candidates = self.get_recommended_candidates(requisition_id, request, db, payload)
                        all_candidates.extend(candidates)
                    elif filter_type == 'rejected':
                        candidates = self.get_rejected_candidates(requisition_id, payload, db)
                        all_candidates.extend(candidates)
                    
                # Remove duplicate candidates (if any) based on a unique identifier (like candidate_id)
                unique_candidates = []
                candidate_ids = set()
                
                for candidate in all_candidates:
                    # For candidate objects, use dot notation or getattr
                    # Check if it's a dict or an object
                    if isinstance(candidate, dict):
                        candidate_id = candidate.get('id')
                    else:
                        # For SQLAlchemy objects or other objects with attributes
                        candidate_id = getattr(candidate, 'id', None) or getattr(candidate, 'user_id', None)
                    
                    if candidate_id and candidate_id not in candidate_ids:
                        candidate_ids.add(candidate_id)
                        unique_candidates.append(candidate)

                
                all_candidates = unique_candidates

            # Special handling for featured candidates
            if filter_types == ['featured']:
                return all_candidates
            else:
                total = len(all_candidates)

                start_idx = (page - 1) * per_page
                end_idx = start_idx + per_page
                paginated_candidates = all_candidates[start_idx:end_idx]

                return {
                    "pagination": {
                        "total": total,
                        "last_page": math.ceil(total / per_page),
                        "page": page,
                        "per_page": per_page,
                    },
                    "items": paginated_candidates,
                }
        except Exception as e:
            raise e



    def get_upcoming_interview_candidates(self, request, requisition_id, db):
        try:
            limit = int(request.query_params.get("limit", 3))
            query = db.query(User).join(Interview.user).filter(
                    Interview.requisition_id == requisition_id,
                )
                # Apply the interview status filters using the helper function
            query = apply_interview_filters(query, ['upcoming'])
            interviews = query.limit(limit).all()
            return jsonable_encoder(interviews)
        except Exception as e:
            raise e
 
    def get_searched_shortlisted_candidates(self, request, db):
        try:
            page = int(request.query_params.get("page", 1))
            per_page = int(request.query_params.get("per_page", 10))
            status = request.query_params.get("status")

            query = (
                db.query(ShortlistedCandidate)
                .options(
                    joinedload(ShortlistedCandidate.user),
                    joinedload(ShortlistedCandidate.requisition),
                )
                .filter(
                        ShortlistedCandidate.recruiter_id == request.state.user.id,
                        ShortlistedCandidate.status == status)
            )
            total = query.count()
            shortlisted_candidates = (
                query.offset((page - 1) * per_page).limit(per_page).all()
            )
            for shortlisted_candidate in shortlisted_candidates:
                check_for_interview = (
                    db.query(Interview)
                    .filter(
                        Interview.requisition_id == shortlisted_candidate.requisition_id,
                        Interview.user_id == shortlisted_candidate.candidate_id,
                    )
                    .first()
                )
                
                if check_for_interview:
                    feedback = (
                        db.query(InterviewFeedback)
                        .filter(
                            InterviewFeedback.requisition_id == shortlisted_candidate.requisition_id,
                            InterviewFeedback.user_id == shortlisted_candidate.candidate_id,
                        )
                        .first()
                    )
                    shortlisted_candidate.feedback_id = feedback.id if feedback else None

                else:
                    shortlisted_candidate.feedback_id = None
                
            return {
                "pagination": {
                    "total": total,
                    "last_page": math.ceil(total / per_page),
                    "page": page,
                    "per_page": per_page,
                },
                "items": jsonable_encoder(shortlisted_candidates),
            }
        except Exception as e:
            raise e