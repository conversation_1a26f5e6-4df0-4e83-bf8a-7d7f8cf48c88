from datetime import date
from schemas.resume import Resume
import base64
from helper.resume_parser import encode_image
import json
from openai import OpenAI
import logging
import boto3
from dependencies import AWS_SECRET_ACCESS_KEY, AWS_ACCESS_KEY_ID, AWS_REGION_BEDROCK, BEDROCK_FUNC_CALLING_MODEL_ID, BEDROCK_MODEL_ID
from botocore.config import Config

logger = logging.getLogger(__name__)

from botocore.config import Config



class BedrockService():
    def __init__(self) -> None:
        self.bedrock_client = boto3.client("bedrock-runtime", region_name=AWS_REGION_BEDROCK, aws_access_key_id=AWS_ACCESS_KEY_ID, aws_secret_access_key=AWS_SECRET_ACCESS_KEY, config = Config(read_timeout=5000)
        )

        self.system_prompt_scan_resume = """You are a resume parser. I will provide you with a resume, and you will extract the relevant fields.\n\n- Always provide dates in YYYY-MM-DD format. \n- If any part of a date is incomplete in the resume, assume the first day of the month (for day) or January (for month) to fill gaps.\n- If any date isn't mentioned or missing, in any section, then DO NOT assume the date, since it is totally missing and not incomplete. \n- Only extract details exactly as they appear in the resume.\n- Do not interpret, assume, or infer any missing information.\n- If certain fields are missing, mention "" for that field.\n- If the total work experience is not mentioned, find it by calculating the difference between start date of the first experience and today's date '{today}.'\n- Ensure the total experience is in years and months.\n- Generate summary if not provided.\n
     
        Residential country instruction:
        Ensure that the user's residential address includes the country code, adhering to the ISO 3166-1 alpha-2 standard (two-letter country codes). Do not derive the country code from the work experience or education sections; it should reflect the user's residential country. If the residential country is not explicitly stated in the resume, infer the country code from the phone number.​
     
        -Residential city instruction: Make sure that the city name in the user's residential address is in descriptive and general full form. Do not abbreviate the city, write its full name. It is not necessary to exactly match the city as in the user's residential city address, provide a general residential city address from the resume. Do not include city from the work experience or education section, as the city is the user's residential city.\n

        -Residential state instruction: Make sure that the state name in the user's residential address is in descriptive and general full form. Do not abbreviate the state, write its full name. It is not necessary to exactly match the state as in the user's residential state address, provide a general residential state address from the resume. Do not include state from the work experience or education section, as the state is the user's residential state.\n

       **CRITICAL INSTRUCTIONS YOU MUST ALWAYS FOLLOW**\n
       1. DO NOT pick up the user's country or city from the education or experience section. You should pick it up from the starting text of the resume or from their phone number, not from the education or experiences section. If the phone number is invalid or unavailable, and there is no country mentioned, set country as the default value of empty string. Same instruction goes for city. If the country present at the top of the resume is not present, you must set it to the default value of empty string.\n
       2. For user's experience and education, you should always pick the location from the experience and education section. You should **NOT** make assumptions in the location for user's experience and education, rather simply copy the location information AS IS (exactly), from the particular experience and education.
            """ 

    def llm(self, message, max_tokens=1024, temperature=1.0, model_id=BEDROCK_MODEL_ID):
        try:
            native_request = {
                "prompt": message,
                "max_gen_len": max_tokens,
                "temperature": temperature,

            }

            request = json.dumps(native_request)

            response = self.bedrock_client.invoke_model(
                modelId=model_id, body=request)

            model_response = json.loads(response["body"].read())

            return model_response["generation"]
        except Exception as e:
            logger.error(e)
            raise e

    def llm_json(self, messages, parameters, function_name="jsonResponse", function_description="", max_tokens=8192, temperature=0.1, func_call_model_id=BEDROCK_FUNC_CALLING_MODEL_ID):
        "Method for function calling using Claude 3.7 "
        try:
            
            tools = [
                {
                    "toolSpec": {
                        "name": function_name,
                        "description": function_description,
                        "inputSchema": {"json": parameters.model_json_schema()}
                    },
                }
            ]

            tool_choice = {
                "tool": {"name": function_name}
            }

            response = self.bedrock_client.converse(
                modelId=func_call_model_id,
                messages=messages,
                inferenceConfig={"maxTokens": max_tokens,
                                    "temperature": temperature},
                toolConfig={"tools": tools, "toolChoice": tool_choice},
            )

            response_text = response["output"]["message"]["content"][0]["toolUse"]["input"]

           
            return response_text
        except Exception as e:
            logger.error(e)
            return parameters.default_instance()
        
    
    def analyze_resume(self, image_paths):
        """Analyze resume images using Bedrock LLaMA3 (image + text input) and return structured data"""

        today = date.today().strftime("%Y-%m-%d")
        logger.info(today)

        system_prompt = self.system_prompt_scan_resume.format(today=today)

        # Compose initial message with the system prompt
        messages = [
            {
                "role": "user",
                "content": [
                    {"text": system_prompt}
                ]
            }
        ]

        # Add each image to the message content
        for image_path in image_paths:
            image_data = encode_image(image_path)
            messages[0]["content"].append({
                "image": {
                    "format": "jpeg",  # or "png", depending on input
                    "source": {
                        "bytes": base64.b64decode(image_data)
                    }
                }
            })

        response_text = self.llm_json(messages, Resume, function_name="Resume", function_description="Resume parsed by LLM", max_tokens=8192, temperature=0.1, func_call_model_id=BEDROCK_FUNC_CALLING_MODEL_ID)

        return response_text


    def analyze_resume_from_text(self, resume_text: str):
        """Analyze resume from raw text input and return structured data using Bedrock LLM"""
        
        today = date.today().strftime("%Y-%m-%d")
        logger.info(today)

        system_prompt = self.system_prompt_scan_resume.format(today=today)
        logger.info(f"System prompt: {system_prompt}")

        messages = [
            {
                "role": "user",
                "content": [
                    {"text": f"{system_prompt}\n\n{resume_text}"}
                ]
            }
        ]


        response_text = self.llm_json(
            messages=messages,
            parameters=Resume,
            function_name="Resume",
            function_description="Resume parsed by LLM",
            max_tokens=8192,
            temperature=0.1,
            func_call_model_id=BEDROCK_FUNC_CALLING_MODEL_ID
        )
        return response_text
