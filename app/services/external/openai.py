from models.organization import OrganizationDetail
import os
import uuid
from helper.resume_parser import encode_image
import json
import openai
from dependencies import OPENAI_API_KEY, LLM_BASE_URL
from fastapi import Depends
from database import get_db
from sqlalchemy.orm import Session
from models.interview import Interview
from models.requisition import Requisition
from models.user import User
from models.skill import Skill
from models.organization import Organization
from models.question_feedback import QuestionFeedback
from schemas.vapi import JobRequirementSchema,RecruiterRequisitionCreateSchema,RequisitionProfession

from schemas.requirements_and_skills import (
    RequirementsAndSkills,
)
from schemas.query_context_classification import QueryContextClassification
from schemas.interview_prep import Categories, Classification, CustomQuestion, Feedback, ResponseModel, interview_feedback
from schemas.query_context_classification import QueryContextClassification, TotalEvaluatedFilters
from schemas.interview_prep import Categories, Ques, QuesFeedback, Classification, Questions, CustomQuestion, Feedback, ResponseModel, interview_feedback, KPIs
from schemas.interview_feedback import AIgeneratedcontentpercentage
from schemas.evaluation_criteria import EvaluationCriteria
from schemas.update_user_profile import UpdateUserProfile
from schemas.resume import Resume
import logging
from schemas.audio_segment import AudioSegment
from schemas.skill_parsing import SkillParsing
from schemas.req_parsing import ReqParsing
from schemas.strengths_and_weaknesses import StrengthsAndWeaknesses
from schemas.question_summary import QuestionSummary
from datetime import date
from schemas.req_processing import CleanedReq
from typing import List
from schemas.requirements_and_skills import Requirement
from schemas.requisition import RequisitionRequirementsSchema,RequisitionRequirementsEvaluationSchema


logger = logging.getLogger(__name__)


class OpenAIServiceClass:
    def __init__(self):
        self.async_client = openai.AsyncOpenAI(api_key=OPENAI_API_KEY)
        self.client = openai.OpenAI(api_key=OPENAI_API_KEY)
        self.llama_client = openai.OpenAI(
            api_key="EMPTY", base_url=LLM_BASE_URL)

        self.gpt_input_token_cost = 0.0000025
        self.gpt_output_token_cost = 0.00001
        self.embedding_cost_per_1000_tokens = 0.00002

        self.system_prompt_resume_parser = """You are a resume parser. I will provide you with a resume, and you will extract the relevant fields.\n\n- Always provide dates in YYYY-MM-DD format. \n- If any part of a date is incomplete in the resume, assume the first day of the month (for day) or January (for month) to fill gaps.\n- If any date isn't mentioned or missing, in any section, then DO NOT assume the date, since it is totally missing and not incomplete. \n- Only extract details exactly as they appear in the resume.\n- Do not interpret, assume, or infer any missing information.\n- If certain fields are missing, mention "" for that field.\n- If the total work experience is not mentioned, find it by calculating the difference between start date of the first experience and today's date '{today}.'\n- Ensure the total experience is in years and months.\n- Generate summary if not provided.\n
     
        Residential country instruction:
        Ensure that the user's residential address includes the country code, adhering to the ISO 3166-1 alpha-2 standard (two-letter country codes). Do not derive the country code from the work experience or education sections; it should reflect the user's residential country. If the residential country is not explicitly stated in the resume, infer the country code from the phone number.​
     
        -Residential city instruction: Make sure that the city name in the user's residential address is in descriptive and general full form. Do not abbreviate the city, write its full name. It is not necessary to exactly match the city as in the user's residential city address, provide a general residential city address from the resume. Do not include city from the work experience or education section, as the city is the user's residential city.\n

        -Residential state instruction: Make sure that the state name in the user's residential address is in descriptive and general full form. Do not abbreviate the state, write its full name. It is not necessary to exactly match the state as in the user's residential state address, provide a general residential state address from the resume. Do not include state from the work experience or education section, as the state is the user's residential state.\n

       **CRITICAL INSTRUCTIONS YOU MUST ALWAYS FOLLOW**\n
       1. DO NOT pick up the user's country or city from the education or experience section. You should pick it up from the starting text of the resume or from their phone number, not from the education or experiences section. If the phone number is invalid or unavailable, and there is no country mentioned, set country as the default value of empty string. Same instruction goes for city. If the country present at the top of the resume is not present, you must set it to the default value of empty string.\n
       2. For user's experience and education, you should always pick the location from the experience and education section. You should **NOT** make assumptions in the location for user's experience and education, rather simply copy the location information AS IS (exactly), from the particular experience and education.
       \n"""

        self.system_prompt_interview = """Your task is to thoroughly evaluate the candidate using the provided candidate's resume, interview transcript, and job requisition's evaluation criteria.

Ensure the evaluation adheres to the following guidelines:

<evaluation_guidelines>
1. **Interview as Primary Evidence**: Prioritize interview responses over the resume for assessment.  
- Penalize vague statements or those lacking examples from the candidate’s past experience with **low percentage (0-20%)**.
- Assign **high percentage (70-100%)** if a requirement is fulfilled through interview.  
- If a requirement is fulfilled through resume, then assign **low percentage (<70%)**.  

2. **Unaddressed Requirements**: If a requirement is missing from both the interview and resume:  
- Set `evaluation` to: *"This requirement remains unaddressed."*  
- Set `statement` to `"N/A"`.
- Assign a **0%** percentage.

3. **Tools & Platforms**: For any tool or platform mentioned in the evaluation criteria:-
- If the candidate have experience with the exact tool or platform mentioned in the evaluation criteria, evaluate it with high percentage (70-100%).
- If the candidate have experience with similar tool or platform, evaluate it with average percentage (50-70%).
- If the candidate have competency with inhouse (unfamiliar) tool or platform, evaluate it with lower percentage (less than 50%).
- If the candidate have no competence with that particular tool or platform, evaluate it with 0 percentage.

4. **Complete Coverage**: Evaluate **every** requirement and skill from the evaluation criteria (except location, which is addressed separately in the `location_satisfied` key). **Do not skip any requirement.**  

5. **Prestigious Company Factor**: If the candidate has worked at a **top-tier company** (e.g., industry leader, billion-dollar company, Fortune 500), assign **70-100%** for relevant experience.

6. **Overqualification Handling**: If the candidate mentions in their resume or interview a level or number of years of experience beyond what is specified in the Evaluation Criteria, then mark `is_overqualified` as true.

7. **Overview**: Provide an overview with a detailed summary of the candidate’s suitability based on the evaluation criteria, structured as bullet points.
</evaluation_guidelines>
 
<inputs>
<candidate_resume>
{resume}
</candidate_resume>

<interview_transcript>
{interview_transcript}
</interview_transcript>

<evaluation_criteria>
{evaluation_criteria}
</evaluation_criteria>
</inputs>"""

        self.system_prompt_binary_evaluation = """Your task is to thoroughly evaluate if the candidate is able to satisfy the given binary job requirement using the provided candidate's interview transcript, and resume.

Ensure the evaluation adheres to the following guidelines:

<scoring_guidelines>
1. Analyze the candidate's response in the interview transcript first:-
- If the candidate's response in the interview transcript is clear, specific, and signals to fulfills the job requirement, then assign a high percentage (100%).
- If the candidate's response in the interview transcript is clear, specific, and signals to not fulfill the job requirement, then assign a low percentage (0%).
- If the response is vague, irrelevant, or shows lack of understanding, move to resume analysis.

2. Candidate Resume Analysis (Only If Transcript Is Vague or Inadequate):-
- If the candidate's response in the interview transcript is, vauge, irrelevant, or if the candidate didn't understand the question, then search through candidate's resume and see if you are able to find any relevant information:
    -- If you are able to find any relevant information in the resume, assign a high percentage (100%).
    -- If you are not able to find any relevant information in the resume, assign a low percentage (0%).

</scoring_guidelines>

<note>
1. Always prioritize the interview response. Use the resume as supplementary evidence when the interview lacks clarity or depth.

2. For any tool or platform mentioned in the job requirement:-
- If the candidate have experience with the exact tool or platform mentioned in the job requirement, assign a high percentage (70% to 100%).
- If the candidate have experience with similar tool or platform, assign an average percentage (50% to 70%).
- If the candidate have competency with inhouse/unfamiliar tool or platform, assign a lower percentage (less than 50%).
- If the candidate have no competence with that particular tool or platform, assign 0%.

3. If the candidate asks for clarification or elaboration on a requirement, do not consider it as a weakness. This is a normal behavior during an interview and should not be penalized.

4. You can ignore the above scoring guidelines if and only if:-
- The candidate has experience at a **top-tier company** (e.g. Fortune 500 companies), then assign a high percentage in range from 70% to 100% only for relevant responses.

5. Do not respond with tags. Just respond with the content only.
</note>

<inputs>
<candidate_resume>
{resume}
</candidate_resume>

<job_requirement>
{job_requirement}
</job_requirement>

<interview_transcript>
{interview_transcript}
</interview_transcript>
</inputs>"""

        self.system_prompt_evaluation = """Your task is to thoroughly evaluate if the candidate is able to fulfill the given job requirement using the provided candidate's interview transcript, and resume.

Ensure the evaluation adheres to the following guidelines:

<scoring_guidelines>
1. Analyze the candidate's response in the interview transcript first:-
- If the candidate's response in the interview transcript is clear, specific, and directly addresses the job requirement, then assign a high percentage (70% to 100%) based on the candidate's response.
- If the response is vague, irrelevant, or shows lack of understanding, move to resume analysis.

2. Candidate Resume Analysis (Only If Transcript Is Vague or Inadequate):-
- If the candidate's response in the interview transcript is vague, irrelevant, or lacks examples, then search through candidate's resume and see if you are able to find any relevant information:
    -- If you are able to find any relevant information in the resume, assign an average percentage (50% to 70%).
    -- If you are not able to find any relevant information in the resume, assign a low percentage (0% to 20%).
</scoring_guidelines>

<note>
1. Always prioritize the interview response. Use the resume as supplementary evidence when the interview lacks clarity or depth.

2. For any tool or platform mentioned in the job requirement:-
- If the candidate have experience with the exact tool or platform mentioned in the job requirement, assign a high percentage (70% to 100%).
- If the candidate have experience with similar tool or platform, assign an average percentage (50% to 70%).
- If the candidate have competency with inhouse/unfamiliar tool or platform, assign a lower percentage (less than 50%).
- If the candidate have no competence with that particular tool or platform, assign 0%.

3. If the candidate asks for clarification or elaboration on a requirement, do not consider it as a weakness. This is a normal behavior during an interview and should not be penalized.

4. You can ignore the above scoring guidelines if and only if:-
- The candidate has experience at a **top-tier company** (e.g. Fortune 500 companies), then assign a high percentage in range from 70% to 100% only for relevant responses.

5. Do not respond with tags. Just respond with the content only.
</note>

<inputs>
<candidate_resume>
{resume}
</candidate_resume>

<job_requirement>
{job_requirement}
</job_requirement>

<interview_transcript>
{interview_transcript}
</interview_transcript>
</inputs>"""

        self.system_prompt_requisition = """Given the candidate's resume, and the job requisition's requirements with evaluation criteria, provide an array of objects for each requirement in the evaluation criteria, except for the location based requirement, as well as an array of objects for each skill mentioned in the resume.\n\n- Each requirement object should contain the following:\n\n1. `requirement`: Represents the specific requirement from the evaluation criteria.\n2. `evaluation`: A detailed assessment of the candidate's proficiency against the criteria of the requirement, focusing on:\n- Their experience level.\n- Quantifiable achievements.\n- Any gaps or lack of articulation.\n3. `percentage`: A numerical score (0-100%) indicating how much the candidate has fulfilled the requirement based on the evaluation criteria.\n- Each skill object should contain the following:\n\n1. `skill`: The name of the skill.\n2. `evaluation`: A detailed assessment of the candidate's proficiency in that skill.\n3. `percentage`: A numerical score (0-100%) indicating how much the candidate has fulfilled the skill based on the evaluation criteria. For any requirement that is binary, assign more than 50% if the candidate passes the requirement, otherwise less than 50%.\n- `Overview`: Assess how well the candidate fits the job evaluation criteria in terms of evaluation criteria. Provide a detailed comment on their suitability in bullet points.\n\n## EVALUATION GUIDELINES\n1. Unaddressed Requirements: For any requirement not mentioned in the resume, strictly evaluate it as 'This requirement remains unaddressed' with a percentage of 0.\n3. Tool/platform coverage: For any tool or platform mentioned in the evaluation criteria:\n- If the candidate have experience with the exact tool or platform mentioned in the evaluation criteria, evaluate it with high percentage (70-100%.)\n- If the candidate have experience with similar tool or platform, evaluate it with average percentage (50-70%.)\n- If the candidate have competency with inhouse (unfamiliar) tool or platform, evaluate it with lower percentage (less than 50%.)\n- If the candidate have no competence with that particular tool or platform, evaluate it with 0 percentage.\n4. Complete Coverage: Ensure that all requirements and skills mentioned in the evaluation criteria are evaluated, even if some receive a 0 percentage. Do not evaluate any location based requirement, ignore location based requirement even if it is mentioned in the evaluation criteria.\n5. Popular-Company: If a candidate has experience of working at a popular company, a company that is considered to be a leader in it's industry, a company that has a billion dollar valuation, or a company which is in fortune 500, evaluate them with a higher percentage (70-100%.)\n\n## RESUME\n{resume}\n\n## REQUISITION EVALUATION CRITERIA\n{evaluation_criteria}"""

        self.system_prompt_strength_and_weakness = """Your task is to identify up to three specific strengths and weaknesses, as well as a list of skills of the candidate relevant to the job role by analyzing the candidate's resume and interview transcript.

**Instructions**

**Strengths**
- Prioritize strengths that are supported by **measurable achievements** or **specific examples** from the interview transcript.
- Ensure that the strengths directly align with the **key requirements** of the job.
- Make sure each pointer is **summarized** and **clearly articulated**.

**Weaknesses**
- Identify only **contextually relevant** weaknesses mentioned by the candidate in the interview.
- **Explicitly exclude** instances where the candidate seeks clarification, as this is a normal and expected behavior during an interview.
- **Do not consider it a weakness** if the candidate explicitly states that they do not know the answer to a question or expresses uncertainty about a particular topic.

**Skills**
- Identify and assess all the skills mentioned in the job requirements against the candidate's resume and interview transcript.

**Note**
- Use the **interview transcript** as the **most reliable source** for evaluating the candidate's experience and qualifications.
- The **resume serves as supplementary context** but should not outweigh insights from the interview.
- Provide a precise, objective analysis focused on how well the candidate's strengths and potential areas for improvement align with the role's **core responsibilities**.


**Candidate Resume**  
{resume}

**Interview Transcript**  
{interview_transcript}

**Job Requirements**  
{evaluation_criteria}"""

        self.system_prompt_evaluation_criteria = """
You are a **Job Analyzer** with expertise in candidate evaluation. You will be provided with two separate lists: one containing **technical requirements** and the other containing **binary requirements** for a specific job role. Your task is to generate a **detailed and structured evaluation framework** based on these requirements for assessing candidates in an **Online Voice Interview** environment.

---

###**Instructions**

#### 1. **Evaluation Criteria**:
- For each requirement, extract **clear, specific, and measurable evaluation criteria**.
- Criteria should reflect both **technical skills** and **soft skills** where applicable.
- Ensure that each criterion can be reasonably assessed in a **voice interview setting**.
- Criteria must **not be redundant** and must directly align with the requirement.

#### 2. **Binary Flag**:
- If the requirement comes from the **binary requirements list**, set `"is_binary": true`.  
- If the requirement comes from the **technical requirements list**, set `"is_binary": false`.

#### 3. **Weightage Allocation**:
- Assign a **weightage (percentage)** to each requirement based on its **importance to the job**.
- Higher weightage should be given to **core or critical requirements**.
- All requirements, including **binary ones**, must have a weightage (even if evaluated as **Pass/Fail**).
- Ensure **total weightage across all requirements can later sum up to 100% when combined**.

#### 4. **Clarity and Consistency**:
- Ensure evaluation criteria are **precise, non-overlapping**, and aligned with the **nature of the requirement**.
- Avoid vague language. Each criterion should **suggest how it will be assessed** during an interview.
- No duplication of evaluation criteria across different requirements.

#### 5. **Additional Guidance**:
- For **binary requirements**, the evaluation should be treated as **Pass/Fail** but include **criteria that indicate what to verify or ask** during the interview.  
  _Example: "Confirm possession of Bachelor's degree in Computer Science"._
"""

        self.check_custom_question = """You are given a question by the user. The user is essentially preparing for a technical or non-technical interview and he has given a question to which he needs to prepare. Your job is to determine whether that question is related to preparation, or is it related to entirely something else. Therefore, output a category called "interview" to indicate that the question is related to interivew preparation. Otherwise, output "not-related". The interview question is: {interview_question}
        """
        self.system_prompt_generate_answer_custom_question = """You are a recruitment expert specializing in creating answers for a given question. Based on the provided question {question} and some additional context (if available) for the question {context}, you need to generate a comprehensive example answer with 3 tips on how to answer the question. \n\n Contextual Relevance: If the question can be answered using the provided context (e.g., past projects, work experience, skills, certifications), integrate it into the example answer. Otherwise, generate a general response without it.  Similarly, if the interview question asks for some personal details that are available in the context, you can use the context to generate the answer. Otherwise, generate a general response to the interview question. Only refer to the context IF nceeessary to do so. Ensure the output is returned in a JSON format. """

        self.system_prompt_generate_feedback_to_answer = """Role: You are an interviewer evaluating a candidate’s response to an interview question. You are also given a few tips on how to answer the question, the tips are {tips}. You are also given an example answer indication how an ideal answer should be for the given interview question, the example answer is: {example_answer}. Your task is to analyze the **candidate's answer** to the interview question: Interview question: {interview_question}\nCandidate's Answer: {user_answer}\n**Instructions:**
        
        1. Provide constructive and critical feedback on the candidate’s answer in 2 to 3 lines. 
        2. Provide the feedback in a first person format, as if YOU are talking to the candidate itself.
        3. Use the tips and example answer to generate the feedback to the candidate's answer.
        4. Ensure that your feedback is accurate, specific
        5. Highlight strengths and areas for improvement to guide the candidate effectively.
        6. If the answer is gibberish or irrelvant, provide a strict feedback.
        7. Always answer in a first person format, as if YOU are talking to the candidate    
        8. Use the additional context (only if necessary), to validate the answer. For example, for questions related to experience, you can use the context to validate the answer. Or for questions related to skills or past projects, you can use the context to validate the answer. Similarly, if the interview question asks for some personal details that are available in the context, you can use the context to validate the answer.
        9. Option for detailed answers as they can indicate the candidate's understanding of the question. One-liner answers should generally be considered as BAD RESPONSE, unless consiseness is required in the question itself.
        10. You also need to categorize the answer into three categories as either GODD RESPONSE, AVERAGE RESPONSE, and NOT A GOOD RESPONSE.
        """

        self.system_prompt_categories = """
        You are a recruitment expert specializing in creating personalized and structured interview frameworks. Based on the job title: {job_title}, job description: {job_description}, and the resume: {resume}\n\n
        Generate 4 to 5 interview preparation categories that can assess the candidate holistically, tailored to the job description and resume, with a five-word description. Make sure the categories are personalized and holistic.
        """

        self.system_prompt_questions = """You are a interview expert specializing in creating personalized and structured interview frameworks. Given the input:

        Categories: {categories}
        Context: {context}
        Generate 3 to 4 open-ended interview questions for each category, ensuring they are tailored to the provided context. Additionally, provide:

        Three actionable tips for each question to help candidates prepare.
        A detailed sample answer incorporating the context for personalization when relevant. If the context is not applicable, construct a general but high-quality response.
        
        Instructions:
        1. Personalization: You need to generate questions keeping personalization in mind. The context might have the job title, resume, or the job description, therefore, you need to generate questions for the categories based on the information provided.
        2. Contextual Relevance: If the question can be answered using the provided context (e.g., past projects, work experience, skills, certifications), integrate it into the example answer. Otherwise, generate a general response without it.  Similarly, if the interview question asks for some personal details that are available in the context, you can use the context to generate the answer.But do not un-necessarily include the context, or make stuff up, if not needed. Otherwise, generate a general response to the interview question. Only refer to the context IF nceeessary to do so.
        3. Practical and Specific Tips: Ensure each tip is directly useful for candidates preparing for the interview. Avoid generic advice.
        4. Example answer instructions: If the example answer can be generated using the context (e.g., resume, work experience, projects etc) then do that. Otherwise, provide a made-up answer.
        5. Content Filtering: If a category is missing, undefined, or incomplete, skip generating questions, tips, and answers for it.
        6. Professional Tone: Maintain a structured and professional format to align with best practices in recruitment.
        """

        self.system_prompt_generate_interview_feedback = """
        You are a critical expert interviewer whose role is to create a consise feedback to user's answers against an interview question. Given the pairs of questions and their corresponding answers: {context} \n Provide a consise feedback in points highlighting potential strengths and weaknesses in the user's answers against the interview question. Generate the feedback directly, no need to respond with the user's questions or answers. Provide a holistic feedback in points, each point highlighting a specific aspect/weakness/improvement areas etc. Output the response in the given format. If the answers are not given or unavailable, provide a critical feedback.
        """

        self.system_prompt_query_classification = """
You are a system that extracts structured data from a natural language query. The input query is: {query}

Perform the following tasks:

---

## TASK 1: Rephrase Query to Focus on Skills/Experience Only

Rephrase the input query to **only retain information about skills and/or experience**. Remove any mention of:

- Specific company or organization names
- Years of experience or time durations
- Locations (cities, states, countries)
- Educational institutions
- Employers or job transitions

The goal is to cleanly isolate only the **role, responsibilities, and relevant skills** (technical or soft) from the query.

**Examples:**
- "Python developer with experience in AI working at Gaditek, 4 years" → "Python developer with experience in AI"
- "React engineer with 5 years at Meta" → "React engineer"
- "Cloud architect with AWS, Azure experience in USA" → "Cloud architect with AWS, Azure experience"

Return the rephrased query as a string under the field: `rephrased_query`.

---

## TASK 2: Classify the `rephrased_query` Type.

Return a boolean for each of the following:
- `candidate_skills`: True if the query mentions skills (technical or soft). Otherwise False.
- `work_experience`: Only true if the query mentions roles, job titles, responsibilities, or projects". Otherwise False

**IMPORTANT**
Do not classify the original query, rather, the rephrased_query
---

## TASK 3: Extract Filters

From the query, extract values for the following filters: {filters}  
Return a dictionary where each key is the filter name, and the value is the extracted value (or null if not found).

**Examples:**
- "AI engineer in USA" → `country`: "United States of America"
- "5 years of experience at Google" → `exp_greater_than_or_equals`: 5, `organization`: "Google"

**Important Notes:**
- If the query mentions **exactly N years** or simply says "**N years of experience**" (e.g., "5 years", "exactly 5 years"), then:
  - Set both `exp_greater_than_or_equals` and `exp_less_than_or_equals` to that number (e.g., 5), to reflect an exact match.
- If the query mentions **more than**, **at least**, or **greater than**:
  - Set `exp_greater_than_or_equals` accordingly, and set `exp_less_than_or_equals` to `None`.
- If the query mentions **less than**, **up to**, or **no more than**:
  - Set `exp_less_than_or_equals` accordingly, and set `exp_greater_than_or_equals` to `None`.
- If multiple countries or cities are mentioned, return them all.
- If a region or a continent is specified, then return the maximum 10 common countries in that region, not cities.

---

## TASK 4: Extract Pins

Pins are for highlighting relevant context and are **independent** of filters.

Extract pins **from the following list** if explicitly found in the query. Do not include pins from below that are NOT present in the query.
- `Job title` — any specific job title
- `Companies` — any specific company names
- `Education` — any educational institutions
- `Location` — any city, state, or country mentioned
    - If multiple locations (cities, states, or countries) are mentioned, return them as a **comma-separated string**
- `Skills` — comma-separated string of skills mentioned in the query
- `Experience` — a free-text form (e.g., "2-4 years")
- `Industry` — any domain or industry mentioned in the query

**IMPORTANT:**
- Only include pins that are explicitly present in the query.
- Do **not** include pins with `None` values — omit them entirely if not present.
- Keep values short and clean (just a few words).
- Use a **comma-separated string** for multiple values (especially for `Skills` and `Location`).

---

Return a single JSON object containing:
- `rephrased_query` (from Task 1)
- `query_type` (from Task 2)
- `filters` (from Task 3)
- `pins` (from Task 4)
"""

        self.system_prompt_recruiter_requisition = """
You are an AI assistant tasked with extracting detailed job requirement information from the provided conversation transcript between a recruiter and an assistant. Your job is to analyze the full transcript and extract information according to the specified response format.

For each field, use only information that is explicitly stated or clearly implied in the provided conversation transcript. If a detail is not provided or is ambiguous, return null for that field. Avoid overly brief or generic responses, and ensure the extracted information is as comprehensive as possible.

Respond strictly in the specified response format defined by the provided schema. Do not include any explanatory text or commentary—only the structured data as output.
"""
        self.system_prompt_mapping_recruiter_requisition = """
## **Objective**

Given the provided parsed data, accurately map the information into the specified output structure. Ensure that each field is correctly extracted and formatted based on the provided data, capturing all essential details for a comprehensive job listing.

## **Instructions**

### **1. Title Extraction:**
- Extract the job title from the `"job_title"` field in the parsed data.
- If the title includes specific levels (e.g., Senior, Junior), retain this detail.

### **2. Brief Description:**
- Generate a concise job description by analyzing the core responsibilities, skills, and experience required from the parsed data. Also, include relevant details from the "company_description" field to capture the essence of the company's mission and culture.

### **3. Salary Range:**
- Extract the salary range from the `"salary_details"` field.

### **4. Location Type:**
- Determine the work location type (e.g., On-site, Remote, Hybrid) based on the `"location_requirements"` and `"onsite_or_hybrid_details"` fields.

### **5. Job Type:**
- Extract the job type (e.g., Full-time, Part-time, Contract) if available. If not explicitly stated, use context from the experience and role expectations.

### **6. Level:**
- Determine the job level based on the `"required_experience_years"` field, using the following criteria:
  - **Junior Level:** 1-2 years
  - **Mid Level:** 2-3 years
  - **Senior Level:** 3-4 years
  - **Manager/Lead Level:** 5+ years

### **7. Long Description:**
- Create a comprehensive job description in HTML format, including the following sections:
    - **Description:** Overview of the role, expectations, and company context.Extract the company context from the "company_description" field in the provided parsed data.
    - **Responsibilities:** Key tasks and responsibilities for this position.
    - **About the Role:** Details about the team structure, potential career growth, and work environment.
    - **Required Years of Experience:** Clearly state the years of experience needed based on the `"required_experience_years"` field.

### **8. Show Salary:**
- Extract this as a boolean from the `"show_salary"` field in the `"salary_details"` section.

### **8. Salary Details:**
- Extract the salary details that are mentioned in the provided parsed data including the currency.

### **9. LLM Requisition Details:**
- Use the parsed data from the fields "must_have_technical_skills", "preferred_previous_positions", and "preferred_industries" to construct a detailed and structured list of major technical requirements and role fit expectations. Do not simply restate the field content. 
- Ensure that each requirement focuses on a single main area of expertise or skill.



### **10. Binary Requirements:**
- Use the fields "required_experience_years", "specific_tools_technologies_certifications", and "negative_constraints" to generate clear and detailed binary job requirements.
- Ensure that each requirement focuses on a single main area of expertise or skill.
"""

        self.system_prompt_professions = """
        # Objective:
The task is to select the most appropriate profession name from a provided list of professions that best matches the given job title. The goal is to identify the profession that aligns most closely with the job description and responsibilities described by the title.

# Instructions:
- You will be provided with a list of **profession names** and a **job title**.
- Based on the job title, your task is to return the **single profession name** that most accurately describes the role.

# Steps:
1. Review the provided **job title** carefully.
2. Consider the typical tasks, skills, and responsibilities associated with the **job title**.
3. Compare the job title with the list of **profession names**.
4. Select the **profession name** that best aligns with the job title in terms of industry, work tasks, and job description.
"""

        self.system_prompt_scan_resume = """You are a resume parser. I will provide you with a resume, and you will extract the relevant fields.\n\n- Always provide dates in YYYY-MM-DD format. \n- If any part of a date is incomplete in the resume, assume the first day of the month (for day) or January (for month) to fill gaps.\n- If any date isn't mentioned or missing, in any section, then DO NOT assume the date, since it is totally missing and not incomplete. \n- Only extract details exactly as they appear in the resume.\n- Do not interpret, assume, or infer any missing information.\n- If certain fields are missing, mention "" for that field.\n- If the total work experience is not mentioned, find it by calculating the difference between start date of the first experience and today's date '{today}.'\n- Ensure the total experience is in years and months.\n- Generate summary if not provided.\n
     
        Residential country instruction:
        Ensure that the user's residential address includes the country code, adhering to the ISO 3166-1 alpha-2 standard (two-letter country codes). Do not derive the country code from the work experience or education sections; it should reflect the user's residential country. If the residential country is not explicitly stated in the resume, infer the country code from the phone number.​
     
        -Residential city instruction: Make sure that the city name in the user's residential address is in descriptive and general full form. Do not abbreviate the city, write its full name. It is not necessary to exactly match the city as in the user's residential city address, provide a general residential city address from the resume. Do not include city from the work experience or education section, as the city is the user's residential city.\n

        -Residential state instruction: Make sure that the state name in the user's residential address is in descriptive and general full form. Do not abbreviate the state, write its full name. It is not necessary to exactly match the state as in the user's residential state address, provide a general residential state address from the resume. Do not include state from the work experience or education section, as the state is the user's residential state.\n

       **CRITICAL INSTRUCTIONS YOU MUST ALWAYS FOLLOW**\n
       1. DO NOT pick up the user's country or city from the education or experience section. You should pick it up from the starting text of the resume or from their phone number, not from the education or experiences section. If the phone number is invalid or unavailable, and there is no country mentioned, set country as the default value of empty string. Same instruction goes for city. If the country present at the top of the resume is not present, you must set it to the default value of empty string.\n
       2. For user's experience and education, you should always pick the location from the experience and education section. You should **NOT** make assumptions in the location for user's experience and education, rather simply copy the location information AS IS (exactly), from the particular experience and education.
            """ 
        self.system_prompt_requirement_evaluation="""
Please evaluate the attached candidate's CV and transcript in relation to the specified requirements. Provide a detailed analysis for each requirement, referencing the candidate's qualifications and experience. Additionally, mention the relevant company name in the analysis wherever applicable. Ensure that the response is thorough and aligns with the criteria provided.
"""

        self.system_prompt_requisition_requirements="""**Prompt:**

As an experienced Hiring Manager with a deep understanding of the relevant industry, you will be provided with a Job Description (JD). Your task is to carefully review the JD and extract the following information:

1. **Mandatory (Required) Qualifications, Skills, and Experience**  
   List all the qualifications, skills, and experience that are explicitly required for the role.

2. **Preferred (Desirable) Qualifications, Skills, and Experience**  
   List all the qualifications, skills, and experience that are desirable but not mandatory for the role."""
        self.system_prompt_match_search_filters = """
        You are an expert data analyst.

        You will be provided with:
        - A dictionary containing candidate information.
        - A dictionary of search filters containing expected values.

        Your task is to evaluate each filter in relation to the candidate data.

        For each filter, output the following fields:
        - `filter_key`: The name of the filter field.
        - `value`: The corresponding value from the candidate data **if a match is found**. Set to `None` if no relevant match exists.
        - `match_status`: One of the following:
            • "present" — the candidate fully satisfies the filter condition.
            • "partially_present" — the candidate partially satisfies the condition based on nuanced logic.
            • "absent" — the candidate does not meet the requirement at all (either the value is missing or fails the threshold).

        - `reason`: A short, clear explanation of why the value is labeled as present, partially_present, or absent.

        Guidelines by data type:

        1. **Lists (e.g., skills, organizations, tools):**
        - "present" if all or any expected values are clearly found in the candidate data.
        - "partially_present" if only some expected items are matched, and others are missing.
        - "absent" if none of the expected items are found.
        - The `value` should contain the matched list items, or be `None` if absent.

        2. **Strings (e.g., job title, location):**
        - "present" if there's an exact or strong match.
        - "partially_present" if there's semantic similarity or partial match.
        - "absent" if unrelated or no match.
        - The `value` should be the candidate's matching string, or `None`.

        3. **Numerical values (e.g., years of experience):**
        - "present" if the candidate meets or exceeds the expected value.
        - "partially_present" if slightly below (within a 0.5 margin).
        - "absent" if well below.
        - `value` should be the candidate’s numeric value if applicable.

        4. **Booleans / Flags:**
        - "present" if the boolean matches exactly.
        - "absent" if it doesn’t.
        - `value` should reflect the candidate's boolean value, or `None`.

        Important:
        - Do **not** penalize candidates for extra data.
        - Your reasoning must justify each match decision based on the logic above.
        - Provide the filters keys and their values in sentence case format (e.g., Job titles, Industry, Experience, Education etc etc)
        - ALWAYS output **ALL the filters in your response**.
        - For filters key that are strings or lists, keep them CONSISE. Not more than a single line, just highlight one or two main things

        Input:
        - Candidate data: {candidate_data}
        - Filters: {filters}
        """
        
    def analyze_resume(self, image_paths):
            """Analyze resume images with GPT-4o and return structured data"""
            today = date.today().strftime("%Y-%m-%d")
            logger.info(today)
            system_prompt = self.system_prompt_scan_resume.format(today=today)
            # Prepare message content
            content = [
                {
                    "type": "text",
                    "text": system_prompt
                }
            ]

            # Add images to content
            for image_path in image_paths:
                content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{encode_image(image_path)}"
                    }
                })
            completion = self.client.chat.completions.create(
                        # model="meta-llama/llama-4-scout-17b-16e-instruct",
                        model="gpt-4o",
                        messages=[
                            {
                                "role": "system",
                                "content": system_prompt,
                            },
                            {"role": "user", "content": content},
                        ],
                        tools=[
                            {
                                "type": "function",
                                "function": {
                                    "name": "Resume",
                                    "description": "Candidate's resume.",
                                    "parameters": Resume.model_json_schema(),
                                },
                            }
                        ],
                        tool_choice={
                            "type": "function",
                            "function": {"name": "Resume"},
                        },
                        temperature=0.1,
                    )
            response = json.loads(
                completion.choices[0].message.tool_calls[0].function.arguments)
            logger.info(response)
            return response
    
    def match_search_filters(self, person_data, filters):
        logger.info("Starting filter matching...")
        system_prompt = self.system_prompt_match_search_filters.format(candidate_data = person_data, filters=filters)

        completion = self.client.chat.completions.create(
            model="gpt-4o",  # whichever model
            messages=[
                {"role": "user", "content": system_prompt},
            ],
            # 3. Use function-calling format to ensure JSON structure
            tools=[
                {
                    "type": "function",
                    "function": {
                        "name": "TotalMatchedFilters",
                        "description": "List of filters",
                        "parameters": TotalEvaluatedFilters.model_json_schema(),
                    },
                }
            ],
            tool_choice={
                "type": "function",
                "function": {"name": "TotalMatchedFilters"},
            },
            temperature=0.0,
        )

        # 4. Extract the tool arguments (JSON) and parse them with Pydantic
        filters = json.loads(
            completion.choices[0].message.tool_calls[0].function.arguments)
        return filters


    def classify_query_context(self, query: str) -> QueryContextClassification:
        logger.info("Starting query classification...")
        """
        Determine whether the given query is related to the 'user_skills',
        'user_work_experience', or both.

        This method does not receive or reference any actual user data or placeholders;
        the references to those columns are hard-coded into the prompt.
        """

        # 1. Build the prompt using only the user’s query
        system_prompt = self.system_prompt_query_classification.format(
            query=query, filters=["industry", "organization", "exp_less_than_or_equals", "exp_greater_than_or_equals", "city", "country", "state", "location_preferences", "expected_salary", "notice_period"])

        # 2. Call the model
        completion = self.client.chat.completions.create(
            model="gpt-4o",  # whichever model
            messages=[
                {"role": "user", "content": system_prompt},
            ],
            # 3. Use function-calling format to ensure JSON structure
            tools=[
                {
                    "type": "function",
                    "function": {
                        "name": "QueryContextClassification",
                        "description": "Return whether sentence is related to work experience or candidate skills",
                        "parameters": QueryContextClassification.model_json_schema(),
                    },
                }
            ],
            tool_choice={
                "type": "function",
                "function": {"name": "QueryContextClassification"},
            },
            temperature=0.0,
        )

        # 4. Extract the tool arguments (JSON) and parse them with Pydantic
        classification = json.loads(
            completion.choices[0].message.tool_calls[0].function.arguments)
        logger.info("gg")
        logger.info(classification)
        return classification

    def generate_interview_feedback(self, context):
        system_prompt = self.system_prompt_generate_interview_feedback.format(
            context=context)

        completion = self.client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "user",
                    "content": system_prompt,
                }
            ],
            tools=[
                {
                    "type": "function",
                    "function": {
                        "name": "interview_feedback",
                        "description": "A dictionary containing the feedback of the interview in list` ",
                        "parameters": interview_feedback.model_json_schema(),
                    },
                }
            ],
            tool_choice={
                "type": "function",
                "function": {"name": "interview_feedback"},
            },
            temperature=0.9,
        )

        # print(completion.choices[0].message.tool_calls[0].function.arguments)
        logger.info("Overall interview feedback generated is ", json.loads(
            completion.choices[0].message.tool_calls[0].function.arguments))

        return json.loads(completion.choices[0].message.tool_calls[0].function.arguments)

    def check_question(self, question):
        system_prompt = self.check_custom_question.format(
            interview_question=question)

        completion = self.client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "user",
                    "content": system_prompt,
                }
            ],
            tools=[
                {
                    "type": "function",
                    "function": {
                        "name": "Classification",
                        "description": "A dictionary containing the classification to the interview question as either `interview` or `not-related` ",
                        "parameters": Classification.model_json_schema(),
                    },
                }
            ],
            tool_choice={
                "type": "function",
                "function": {"name": "Classification"},
            },
            temperature=0.9,
        )

        # print(completion.choices[0].message.tool_calls[0].function.arguments)

        return json.loads(completion.choices[0].message.tool_calls[0].function.arguments)

    def generate_feedback(self, context, question, answer):
        job_title = context['job_title']
        job_description = context['job_description']
        resume = context['resume']
        example_answer = context['example_answer']
        tips = context['tips']

        system_prompt = self.system_prompt_generate_feedback_to_answer.format(
            interview_question=question,
            user_answer=answer,
            tips=tips,
            example_answer=example_answer

        )

        completion = self.client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "user",
                    "content": system_prompt,
                }
            ],
            tools=[
                {
                    "type": "function",
                    "function": {
                        "name": "Feedback",
                        "description": "A dictionary containing the feedback to the user's answer against the interview question and the rating as either GOOD RESPONSE, NOT A GOOD RESPONSE, or AVERAGE RESPONSE.",
                        "parameters": Feedback.model_json_schema(),
                    },
                }
            ],
            tool_choice={
                "type": "function",
                "function": {"name": "Feedback"},
            },
            temperature=0.1,
        )

        logger.info("Feedback generated is ", json.loads(
            completion.choices[0].message.tool_calls[0].function.arguments))

        return json.loads(completion.choices[0].message.tool_calls[0].function.arguments)

    def custom_generate(self, question, context):
        system_prompt = self.system_prompt_generate_answer_custom_question.format(
            question=question,
            context=context
        )

        completion = self.client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "user",
                    "content": system_prompt,
                }
            ],
            tools=[
                {
                    "type": "function",
                    "function": {
                        "name": "CustomQuestion",
                        "description": "A dictionary containing the tips, and a detailed example answer.",
                        "parameters": CustomQuestion.model_json_schema(),
                    },
                }
            ],
            tool_choice={
                "type": "function",
                "function": {"name": "CustomQuestion"},
            },
            temperature=0.7,
        )

        return json.loads(completion.choices[0].message.tool_calls[0].function.arguments)

    def generate_categories(self, job_title, job_description, resume):
        system_prompt = self.system_prompt_categories.format(
            job_title=job_title, job_description=job_description, resume=resume)

        logger.info("Generating categories...")
        completion = self.client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "user",
                    "content": system_prompt,
                }
            ], tools=[
                {
                    "type": "function",
                    "function": {
                            "name": "Categories",
                            "description": "Interview categories generated in the given format.",
                            "parameters": Categories.model_json_schema(),
                    },
                }
            ],
            tool_choice={
                "type": "function",
                "function": {"name": "Categories"},
            },
            temperature=0.7
        )
        categories = json.loads(
            completion.choices[0].message.tool_calls[0].function.arguments)

        logger.info(categories['categories'])
        logger.info("Finished category generation")

        return categories['categories']

    def generate_questions(self, categories, context):
        system_prompt = self.system_prompt_questions.format(
            categories=categories, context=context)

        completion = self.client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "user",
                    "content": system_prompt,
                }
            ], tools=[
                {
                    "type": "function",
                    "function": {
                            "name": "ResponseModel",
                            "description": "Interview questions generated in the given format.",
                            "parameters": ResponseModel.model_json_schema(),
                    },
                }
            ],
            tool_choice={
                "type": "function",
                "function": {"name": "ResponseModel"},
            },
            temperature=0.7
        )

        questions = json.loads(
            completion.choices[0].message.tool_calls[0].function.arguments)
        logger.info("Interview prep questions generated are ", questions)

        return questions

    def calculate_percentages(self, requirements: dict, do_not_evaluate_unaddressed=False):
        overall_percentage = 0
        weighted_percentage = 0
        satisfies_binary_requirements = True
        total_weightage = 0
        count = 0

        for requirement in requirements:
            if requirement["is_unaddressed"] and do_not_evaluate_unaddressed and not requirement["is_binary"]:
                continue

            if not requirement["is_binary"]:
                overall_percentage += requirement["percentage"]
                count += 1

                weightage = max(requirement["weightage"], 1)
                weighted_percentage += (requirement["percentage"]
                                        * weightage)
                total_weightage += weightage

            if satisfies_binary_requirements and requirement['is_binary'] and requirement['percentage'] < 70:
                satisfies_binary_requirements = False

        if count:
            overall_percentage = round((overall_percentage / count), 2)
        else:
            overall_percentage = 0

        if total_weightage:
            weighted_percentage = round(
                (weighted_percentage / total_weightage), 2)
        else:
            weighted_percentage = 0

        return overall_percentage, weighted_percentage, satisfies_binary_requirements

    def get_cold_email(self, messages, Cold_email_response, description):
        completion = self.client.chat.completions.create(
            model="gpt-4o",
            messages=messages,
            tools=[
                {
                    "type": "function",
                    "function": {
                            "name": "Cold_email_response",
                            "description": "Cold email generated.",
                            "parameters": Cold_email_response.model_json_schema(),
                    },
                }
            ],
            tool_choice={
                "type": "function",
                "function": {"name": "Cold_email_response"},
            },
            temperature=0.5,
        )

        resp = json.loads(
            completion.choices[0].message.tool_calls[0].function.arguments)
        return resp

    def extract_fields_from_text(self, text: str):
        try:
            logger.info("Extracting fields from resume.")
            if not text:
                return None

            today = date.today().strftime("%Y-%m-%d")
            logger.info(today)

            system_prompt = self.system_prompt_resume_parser.format(
                today=today,
            )

            completion = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "system",
                        "content": system_prompt,
                    },
                    {"role": "user", "content": text},
                ],
                tools=[
                    {
                        "type": "function",
                        "function": {
                            "name": "Resume",
                            "description": "Candidate's resume.",
                            "parameters": Resume.model_json_schema(),
                        },
                    }
                ],
                tool_choice={
                    "type": "function",
                    "function": {"name": "Resume"},
                },
                temperature=0.1,
            )

            prompt_token_cost = completion.usage.prompt_tokens * self.gpt_input_token_cost
            completion_token_cost = completion.usage.completion_tokens * self.gpt_output_token_cost
            total_token_cost = prompt_token_cost + completion_token_cost

            logger.info(
                f"Prompt token cost: ${prompt_token_cost} | Completion token cost: ${completion_token_cost} | Total token cost: ${total_token_cost}")

            logger.info("Extracted fields from resume.")
            return json.loads(completion.choices[0].message.tool_calls[0].function.arguments)
        except Exception as e:
            logger.error(e)
            raise e

    def check_ai_generated_content(self, interview: QuestionFeedback, db: Session = Depends(get_db)):
        """
        Takes the candidate's responses and checks if they are AI-generated or not. Outputs a percentage of how much content is AI-generated.
        """
        try:

            interview_id = interview.id
            user_id = interview.user_id

            logger.info(
                f"Starting check_ai_generated_content function for interview ID: '{interview_id}', user ID: '{user_id}'"
            )

            nl = "\n"
            interview_transcript = "\n----\n".join(
                [
                    f"CANDIDATE: {question_feedback.answer if question_feedback.answer else 'Not answered'}"
                    for question_feedback in db.query(QuestionFeedback)
                    .filter(QuestionFeedback.interview_id == interview_id)
                    .order_by(QuestionFeedback.id.asc())
                    .all()
                ]
            )
            # Call AI-based content detection API
            completion = self.client.beta.chat.completions.parse(
                model="gpt-4o",
                temperature=0.0,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an AI-based text detector. Analyze the following interview script. Assess whether the language, tone, and flow resemble AI-generated text or human-authored dialogue. Provide a percentage score for how likely it is to be AI-generated, along with reasoning for your assessment. Focus on specific indicators such as repetition, unnatural phrasing, context-awareness, creativity, emotional nuance, and adaptability to conversational shifts. Output response in JSON format. Be highly critical.",
                    },
                    {"role": "user", "content": interview_transcript},
                ],
                response_format=AIgeneratedcontentpercentage,
            )

            # Extract parsed response from AI service
            event = completion.choices[0].message.parsed

            # Log the response
            logger.info(
                f"AI-generated content percentage for interview ID {interview.id}: {event.percentage}"
            )
            return event

        except Exception as e:
            # Log the error and return a custom error message
            logger.error(
                f"Error in check_ai_generated_answers for interview ID {interview.id}: {str(e)}")
            return False

    def summarize_interview(self, interview: Interview, db: Session = Depends(get_db)):
        """
        Summarizes the interview and evaluates how well the user fits the job requisition in terms of
        requirements.

        Args:
            interview (Interview): The interview object to summarize.
            db (Session, optional): The SQLAlchemy session to use. Defaults to Depends(get_db).

        Returns:
            dict: A dictionary containing the user's skills, experience, education and an evaluation of how well
            the user fits the job requisition in terms of requirements.
        """

        interview_id = interview.id
        user_id = interview.user_id
        requisition_id = interview.requisition_id

        try:
            logger.info(
                f"Starting summarize_interview function for interview ID: '{interview_id}', user ID: '{user_id}', requisition ID: '{requisition_id}'"
            )

            total_token_cost_requirement = 0
            evaluation = {}
            requirements = []

            evaluation_criteria = db.query(Requisition).filter(
                Requisition.id == requisition_id).first().evaluation_criteria

            resume = db.query(User).filter(User.id == user_id).first()

            if not evaluation_criteria:
                logger.error(
                    f"Evaluation criteria not found for requisition ID: '{requisition_id}'"
                )
                raise Exception(
                    f"Evaluation criteria not found for requisition ID: '{requisition_id}'")

            try:
                user_skills = [
                    db.query(Skill).filter(
                        Skill.id == skill.skill_id).first().name
                    for skill in resume.skills
                ]
            except Exception as e:
                logger.error(f"Error fetching user skills: {e}")
                user_skills = []

            try:
                education = ", ".join(
                    [
                        f"{education.degree} - {education.field} ({education.start_date} - {education.end_date or 'present'})"
                        for education in resume.educations
                    ]
                )
            except Exception as e:
                logger.error(f"Error fetching user education: {e}")
                education = []

            try:
                work_experience = []

                for experience in resume.experiences:
                    organization = db.query(Organization).filter(Organization.id == experience.organization_id).first()
                    org_detail = db.query(OrganizationDetail).filter(
                        OrganizationDetail.organization_id == experience.organization_id
                    ).first()

                    work_experience.append({
                        "organization": organization.name if organization else None,
                        "position": experience.job_title,
                        "duration": f"{experience.from_date} - {experience.to_date or 'present'}",
                        "description": experience.description,
                        "company_information": org_detail.description if org_detail else None
                    })
            except Exception as e:
                logger.error(f"Error fetching user work experience: {e}")
                work_experience = []

            resume = {
                "name": f"{resume.first_name} {resume.last_name}",
                "location": resume.location,
                "total_experience": f"{resume.total_experience}",
                "skills": user_skills,
                "education": education,
                "work_experience": work_experience,
                "about": resume.about
            }

            logger.info(
                "Evaluating requirements, responsibilities, and skills")

            for requirement in evaluation_criteria["criteria"]:
                nl = "\n"
                interview_transcript = "\n----\n".join(
                    [
                        f"<interview_question>{nl}{question_feedback.question}{nl}</interview_question>{nl}<candidate_response>{nl}{question_feedback.answer if question_feedback.answer else 'Not answered!'}{nl}</candidate_response>"
                        for question_feedback in db.query(QuestionFeedback)
                        .filter(QuestionFeedback.interview_id == interview_id, QuestionFeedback.requirement == requirement['requirement'])
                        .order_by(QuestionFeedback.id.asc())
                        .all()
                    ]
                )

                if not interview_transcript:
                    interview_transcript = "\n----\n".join(
                        [
                            f"<interview_question>{nl}{question_feedback.question}{nl}</interview_question>{nl}<candidate_response>{nl}{question_feedback.answer if question_feedback.answer else 'Not answered!'}{nl}</candidate_response>"
                            for question_feedback in db.query(QuestionFeedback)
                            .filter(QuestionFeedback.interview_id == interview_id)
                            .order_by(QuestionFeedback.id.asc())
                            .all()
                        ]
                    )

                if requirement['is_binary']:
                    logger.info("Evaluating binary requirement")
                    system_prompt = self.system_prompt_binary_evaluation.format(
                        resume=resume,
                        job_requirement=requirement['requirement'],
                        interview_transcript=interview_transcript,
                    )
                else:
                    logger.info("Evaluating non-binary requirement")
                    system_prompt = self.system_prompt_evaluation.format(
                        resume=resume,
                        job_requirement=requirement['requirement'],
                        interview_transcript=interview_transcript,
                    )

                per_evaluation = self.client.chat.completions.create(
                    model="gpt-4o",
                    messages=[
                        {
                            "role": "user",
                            "content": system_prompt,
                        }
                    ],
                    tools=[
                        {
                            "type": "function",
                            "function": {
                                "name": "RequirementEvaluation",
                                "description": "Takes evaluation, statement, and percentage.",
                                "parameters": Requirement.model_json_schema(),
                            },
                        }
                    ],
                    tool_choice={
                        "type": "function",
                        "function": {"name": "RequirementEvaluation"},
                    },
                    temperature=0.1,
                )

                prompt_token_cost_requirement = per_evaluation.usage.prompt_tokens * \
                    self.gpt_input_token_cost
                completion_token_cost_requirement = per_evaluation.usage.completion_tokens * \
                    self.gpt_output_token_cost
                total_token_cost_requirement += prompt_token_cost_requirement + \
                    completion_token_cost_requirement

                per_evaluation = per_evaluation.choices[0].message.tool_calls[0].function.arguments
                per_evaluation = json.loads(per_evaluation)

                per_evaluation['requirement'] = requirement['requirement']
                per_evaluation['is_binary'] = requirement['is_binary']
                per_evaluation['weightage'] = requirement['weightage']
                per_evaluation['is_unaddressed'] = False

                requirements.append(per_evaluation)

            logger.info(
                f"Prompt token cost: ${prompt_token_cost_requirement} | Completion token cost: ${completion_token_cost_requirement} | Total token cost: ${total_token_cost_requirement}")

            overall_percentage, weighted_percentage, satisfies_binary_requirements = self.calculate_percentages(
                requirements,
                do_not_evaluate_unaddressed=True
            )

            logger.info("Evaluating strengths and weaknesses")

            system_prompt = self.system_prompt_strength_and_weakness.format(
                resume=resume,
                interview_transcript=interview_transcript,
                evaluation_criteria=evaluation_criteria
            )

            strength_weakness_skills = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "user",
                        "content": system_prompt,
                    }
                ],
                tools=[
                    {
                        "type": "function",
                        "function": {
                            "name": "Overview",
                            "description": "Takes overview, list of strengths, weaknesses, and skills and over-qualification check.",
                            "parameters": StrengthsAndWeaknesses.model_json_schema(),
                        },
                    }
                ],
                tool_choice={
                    "type": "function",
                    "function": {"name": "Overview"},
                },
                temperature=0.5,
            )

            prompt_token_cost_sw = strength_weakness_skills.usage.prompt_tokens * \
                self.gpt_input_token_cost
            completion_token_cost_sw = strength_weakness_skills.usage.completion_tokens * \
                self.gpt_output_token_cost
            total_token_cost_sw = prompt_token_cost_sw + completion_token_cost_sw

            logger.info(
                f"Prompt token cost: ${prompt_token_cost_sw} | Completion token cost: ${completion_token_cost_sw} | Total token cost: ${total_token_cost_sw}")

            strength_weakness_skills = json.loads(strength_weakness_skills.choices[
                0].message.tool_calls[0].function.arguments)

            evaluation['requirements'] = requirements
            evaluation['skills'] = strength_weakness_skills['skills']
            evaluation['relevant_experience'] = resume['total_experience']
            evaluation['strengths'] = strength_weakness_skills['strengths']
            evaluation['weaknesses'] = strength_weakness_skills['weaknesses']
            evaluation['evaluation'] = {
                "overview": strength_weakness_skills['overview'],
                "percentage": overall_percentage,
                "weighted_percentage": weighted_percentage,
                "is_satisfactory": satisfies_binary_requirements and not strength_weakness_skills['is_overqualified'],
                "location_satisfied": True,
                "is_overqualified": strength_weakness_skills['is_overqualified']
            }
            evaluation['total_cost'] = total_token_cost_requirement + \
                total_token_cost_sw

            logger.info(
                f"Successfully evaluated interview of interview ID: '{interview_id}', user ID: '{user_id}'"
            )

            return evaluation
        except Exception as e:
            logger.error(
                f"Error evaluating interview interview ID: '{interview_id}', user ID: '{user_id}'"
            )
            logger.error(e)
            return False

    def evauate_candidate_against_requisition(self, interview_transcript: str, resume: str, evaluation_criteria: EvaluationCriteria):
        logger.info("Evaluating interview against requisition")

        try:
            system_prompt = self.system_prompt_interview.format(
                resume=resume,
                interview_transcript=interview_transcript,
                evaluation_criteria=evaluation_criteria
            )

            extracted_skills = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "user",
                        "content": system_prompt,
                    }
                ],
                tools=[
                    {
                        "type": "function",
                        "function": {
                            "name": "RequirementsAndSkills",
                            "description": "A list of requirements, and skills with evaluation, candidate's statement, and percentage scored.",
                            "parameters": RequirementsAndSkills.model_json_schema(),
                        },
                    }
                ],
                tool_choice={
                    "type": "function",
                    "function": {"name": "RequirementsAndSkills"},
                },
                temperature=0.1,
            )

            prompt_token_cost_skills = extracted_skills.usage.prompt_tokens * \
                self.gpt_input_token_cost
            completion_token_cost_skills = extracted_skills.usage.completion_tokens * \
                self.gpt_output_token_cost
            total_token_cost_skills = prompt_token_cost_skills + completion_token_cost_skills

            logger.info(
                f"Prompt token cost: ${prompt_token_cost_skills} | Completion token cost: ${completion_token_cost_skills} | Total token cost: ${total_token_cost_skills}")

            extracted_skills = (
                extracted_skills.choices[0].message.tool_calls[0].function.arguments
            )

            extracted_skills = json.loads(extracted_skills)

            overall_percentage, weighted_percentage, satisfies_binary_requirements = self.calculate_percentages(
                extracted_skills
            )

            logger.info("Evaluating strengths and weaknesses")

            system_prompt = self.system_prompt_strength_and_weakness.format(
                resume=resume,
                interview_transcript=interview_transcript,
                evaluation_criteria=evaluation_criteria
            )

            strength_and_weakness = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "user",
                        "content": system_prompt,
                    }
                ],
                tools=[
                    {
                        "type": "function",
                        "function": {
                            "name": "StrengthsAndWeaknesses",
                            "description": "A list of strengths, and weaknesses of the candidate.",
                            "parameters": StrengthsAndWeaknesses.model_json_schema(),
                        },
                    }
                ],
                tool_choice={
                    "type": "function",
                    "function": {"name": "StrengthsAndWeaknesses"},
                },
                temperature=0.8,
            )

            prompt_token_cost_sw = strength_and_weakness.usage.prompt_tokens * \
                self.gpt_input_token_cost
            completion_token_cost_sw = strength_and_weakness.usage.completion_tokens * \
                self.gpt_output_token_cost
            total_token_cost_sw = prompt_token_cost_sw + completion_token_cost_sw

            logger.info(
                f"Prompt token cost: ${prompt_token_cost_sw} | Completion token cost: ${completion_token_cost_sw} | Total token cost: ${total_token_cost_sw}")

            strength_and_weakness = json.loads(strength_and_weakness.choices[
                0].message.tool_calls[0].function.arguments)

            response = {
                "requirements": extracted_skills['requirements'],
                "skills": extracted_skills['skills'],
                "relevant_experience": extracted_skills['relevant_experience'],
                "strengths": strength_and_weakness['strengths'],
                "weaknesses": strength_and_weakness['weaknesses'],
                "evaluation": {
                    "overview": extracted_skills["overview"],
                    "percentage": overall_percentage,
                    "weighted_percentage": weighted_percentage,
                    "is_satisfactory": satisfies_binary_requirements
                },
                "prompt_cost": prompt_token_cost_skills + prompt_token_cost_sw,
                "completion_cost": completion_token_cost_skills + completion_token_cost_sw,
                "total_cost": total_token_cost_skills + total_token_cost_sw
            }

            # print(strength_and_weakness['weaknesses'])
            return response
        except Exception as e:
            logger.error(
                f"Error evaluating interview against requisition: {e}"
            )
            raise e

    def evaluate_candidate_with_resume_requisition(self, requisition_id: int, user_id: int, db: Session = Depends(get_db)):
        """
        Evaluates how well the user fits the job requisition in terms of requirements.
        Args:
            interview (Interview): The interview object to summarize.
            db (Session, optional): The SQLAlchemy session to use. Defaults to Depends(get_db).
        Returns:
            dict: A dictionary containing the user's skills, experience, education and an evaluation of how well
            the user fits the job requisition in terms of requirements.
        """

        try:
            logger.info(
                f"Pre-evaluating user ID: '{user_id}', requisition ID: '{requisition_id}'"
            )

            evaluation_criteria = db.query(Requisition).filter(
                Requisition.id == requisition_id).first().evaluation_criteria

            resume = db.query(User).filter(User.id == user_id).first()

            try:
                user_skills = [
                    db.query(Skill).filter(
                        Skill.id == skill.skill_id).first().name
                    for skill in resume.skills
                ]
            except:
                user_skills = []

            try:
                education = ", ".join(
                    [
                        f"{education.degree} - {education.field} ({education.start_date} - {education.end_date or 'present'})"
                        for education in resume.educations
                    ]
                )
            except:
                education = "Not available!"

            try:
                work_experience = [
                    {
                        "organization": db.query(Organization).filter(Organization.id == experience.organization_id).first().name,
                        "position": experience.job_title,
                        "duration": f"{experience.from_date} - {experience.to_date or 'present'}",
                        "description": experience.description,
                    }
                    for index, experience in enumerate(resume.experiences)
                ]
            except:
                work_experience = "Not available!"

            resume = {
                "name": f"{resume.first_name} {resume.last_name}",
                "location": resume.location,
                "total_experience": f"{resume.total_experience}",
                "skills": user_skills,
                "education": education,
                "work_experience": work_experience,
                "about": resume.about
            }

            system_prompt = self.system_prompt_requisition.format(
                resume=resume,
                evaluation_criteria=evaluation_criteria
            )

            extracted_skills = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "user",
                        "content": system_prompt,
                    }
                ],
                tools=[
                    {
                        "type": "function",
                        "function": {
                            "name": "RequirementsAndSkills",
                            "description": "A list of requirements, and skills with candidate's evaluation, statement, and percentage.",
                            "parameters": RequirementsAndSkills.model_json_schema(),
                        },
                    }
                ],
                tool_choice={
                    "type": "function",
                    "function": {"name": "RequirementsAndSkills"},
                },
                temperature=0.1,
            )

            prompt_token_cost = extracted_skills.usage.prompt_tokens * self.gpt_input_token_cost
            completion_token_cost = extracted_skills.usage.completion_tokens * \
                self.gpt_output_token_cost
            total_token_cost = prompt_token_cost + completion_token_cost

            logger.info(
                f"Prompt token cost: ${prompt_token_cost} | Completion token cost: ${completion_token_cost} | Total token cost: ${total_token_cost}")

            extracted_skills = (
                extracted_skills.choices[0].message.tool_calls[0].function.arguments
            )

            extracted_skills = json.loads(extracted_skills)

            overall_percentage, weighted_percentage, satisfies_binary_requirements = self.calculate_percentages(
                extracted_skills
            )

            response = {
                "requirements": extracted_skills['requirements'],
                "skills": extracted_skills['skills'],
                "relevant_experience": extracted_skills['relevant_experience'],
                "evaluation": {
                    "overview": extracted_skills["overview"],
                    "percentage": overall_percentage,
                    "weighted_percentage": weighted_percentage,
                    "is_satisfactory": satisfies_binary_requirements
                },
                "prompt_cost": prompt_token_cost,
                "completion_cost": completion_token_cost,
                "total_cost": total_token_cost
            }

            logger.info(
                f"Successfully pre-evaluated user ID: '{user_id}', requisition ID: '{requisition_id}'"
            )
            return response
        except Exception as e:
            logger.error(
                f"Error pre-evaluating user ID: '{user_id}', requisition ID: '{requisition_id}'"
            )
            logger.error(e)
            return False

    def create_user_profile(self, resume: dict, interview_transcript: str):
        """
        This function is responsible for augmenting the candidate's resume by
        extracting and inserting any missing information from the interview
        transcript into the appropriate sections of the resume.

        Args:
            resume (dict): The candidate's resume.
            interview_transcript (str): The candidate's interview transcript.

        Returns:
            dict: The updated resumet.
        """

        logger.info("Starting create_user_profile function")

        try:
            # Construct the system prompt to update the candidate's profile
            system_prompt = f"""You are provided with a candidate's resume and their interview transcript. Your task is to update the candidate's resume by extracting and updating existing information from the interview transcript into the appropriate sections of the resume.\n\n- Do not infer or assume any information. If details are not explicitly mentioned in either the resume or the interview transcript, mark that field as null.\n- Ensure that the added details are contextually relevant and accurately reflect the interview transcript.\n- Keep the updated resume format consistent with the original.\n\n## INTERVIEW TRANSCRIPT\n{interview_transcript}\n\n## RESUME\n{resume}"""

            # Call the OpenAI API to update the candidate's profile
            user_profile = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "system",
                        "content": system_prompt,
                    }
                ],
                tools=[
                    {
                        "type": "function",
                        "function": {
                            "name": "UpdateUserProfile",
                            "description": "An updated user profile with skills, tools, and experience",
                            "parameters": UpdateUserProfile.model_json_schema(),
                        },
                    }
                ],
                tool_choice={
                    "type": "function",
                    "function": {"name": "UpdateUserProfile"},
                },
                temperature=0,
            )

            # Extract the user profile from the response
            user_profile = json.loads(
                user_profile.choices[0].message.tool_calls[0].function.arguments
            )

            return user_profile
        except Exception as e:
            logger.error(e)
            raise e

    def process_description(self, req_description, req_req, req_res, domain_name):
        completion = self.client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "system",
                    "content": """You are tasked with analyzing job requisitions and extracting relevant information based on specified skill levels and durations.

    Consider the following skill levels and their corresponding experience durations:

    - Junior Level: 1-2 years
    - Mid Level: 2-3 years
    - Senior Level: 3-4 years
    - Manager/Lead Level: 5+ years

    ### Task 1
    Match the provided job requisition text to the appropriate skill level from the options above.

    ### Task 2
    Extract specific skills mentioned in the requisition along with brief descriptions, focusing on keywords and relevant details. Ensure that the skills extracted are related to the provided Domain Name. If a skill is not related to the domain name, skip it.
    ### Response Format
    Your response must strictly follow this format:
    {
    "matched level": "Junior",
    "skills": [
        {
        "name": "",
        "description": ""
        },
        {
        "name": "JavaScript",
        "description": ""
        }
    ]
    }

    Ensure accuracy in level matching and thoroughness in skill extraction, reflecting the proficiency and experience requirements outlined in each job requisition. Skills must be relevant to the provided Domain Name.""",
                },
                {
                    "role": "user",
                    "content": f"""Domain Name: - {domain_name}\n Description: - {req_description}\n Requirements: - {req_req}\n Responsibilities: - {req_res}\n""",
                },
            ],
            tools=[
                {
                    "type": "function",
                    "function": {
                        "name": "RequisitionSkillParsing",
                        "description": "Extracted skills and their corresponding skill levels from the job requisition text",
                        "parameters": SkillParsing.model_json_schema(),
                    },
                }
            ],
            tool_choice={
                "type": "function",
                "function": {"name": "RequisitionSkillParsing"},
            },
        )
        return completion.choices[0].message.tool_calls[0].function.arguments

    def parse_requsitions(self, description):
        completion = self.client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "system",
                    "content": """
    You are given a job requisition description. Your tasks are as follows:

    #### Task 1: Match the Requisition Level
    Determine the appropriate requisition level based on the job requisition text using the criteria below:

    - **Junior Level:** 1-2 years
    - **Mid Level:** 2-3 years
    - **Senior Level:** 3-4 years
    - **Manager/Lead Level:** 5+ years

    #### Task 2: Extract Job Details
    Parse the job requisition text and extract the following details, mapping them to the corresponding fields in the specified response format.

    #### Task 4: Identify the Specific Field of Operation
   Analyze the provided job requisition text to identify the **specific field of operation** of the job role or the company. This should reflect the precise domain or area of expertise relevant to the role and the organization, rather than a broad industry classification.

   #### Task 5: Extract Requirements and Responsibilities
   Take out all the possible **requirements** and **responsibilities** from the provided requisition description. This includes anything and everything explicitly mentioned under the "requirements" or "responsibilities" headings, as well as any relevant mention of expectations, qualifications, skills, experience, or duties throughout the requisition.  

   Make sure to include **anything that sounds like a requirement**, even if it is not explicitly labeled as such. This includes but is not limited to:  

    ### Notes:

    1. If any field specified in the response format is not mentioned in the job requisition, set that key's value to `null`.
    2. Ensure all extracted information accurately reflects the data provided in the job requisition.
    3. Match the requisition level according to the years of experience or role descriptions provided.
    4. Populate the JSON response precisely as per the format, ensuring correctness and completeness.
    5. Double-check your response for accuracy and adherence to the specified format.
    6. Make sure to include every detail mentioned in the provided description without omission.
                """,
                },
                {
                    "role": "user",
                    "content": f"""Job Description: - {description}""",
                },
            ],
            tools=[
                {
                    "type": "function",
                    "function": {
                        "name": "RequisitionParsing",
                        "description": "Parsed job requisition details",
                        "parameters": ReqParsing.model_json_schema(),
                    },
                }
            ],
            tool_choice={
                "type": "function",
                "function": {"name": "RequisitionParsing"},
            },
            temperature=0,
        )

        return completion.choices[0].message.tool_calls[0].function.arguments

    def extract_intro(self, candidate_answer):
        logger.info(f"Process started")
        completion = self.client.chat.completions.create(
            model="gpt-4o",
            messages=[



                {"role": "system",
                    "content": """
### Task Overview
You are tasked with analyzing an array of objects representing a candidate's responses during an interview. Each object contains a user's response and segments of that response. Your goal is to identify the starting and ending points of the candidate's detailed introduction.

### Instructions:

1. **Extract the Response ID**: Identify and extract the id of each response object.

2. **Identify the Starting Segment of the Introduction**:
   - Locate the segment where the candidate's introduction begins

3. **Determine the Ending Segment of the Introduction**:
   - Identify the segment where the candidate's introduction concludes.

### Output Requirements:

Provide the following information in your analysis:
- **Starting Segment**:
  - response_id: The unique identifier for the candidate's response.
  - starting_segment_id: The segment ID from which the candidate's introduction begins.

- **Ending Segment**:
  - response_id: The unique identifier for the candidate's response.
  - ending_segment_id: The segment ID where the candidate's introduction ends.


### Example Response format:
{
    "starting_segment": {
        "response_id": ,
        "starting_segment_id":
    },
    "ending_segment": {
        "response_id": ,
        "ending_segment_id":
    }
}
                """},
                {"role": "user", "content": f"""Candidate Answers: - {candidate_answer}"""},
            ],
            tools=[
                {
                    "type": "function",
                    "function": {
                        "name": "AudioSegment",
                        "description": "Extracts the starting and ending points of a candidate's introduction from an array of objects that represent their responses during an interview.",
                        "parameters": AudioSegment.model_json_schema(),
                    },
                }
            ],
            tool_choice={
                "type": "function",
                "function": {"name": "AudioSegment"},
            },
        )

        logger.info(f"Process ended")
        return completion.choices[0].message.tool_calls[0].function.arguments

    def summarize_response(self, response):
        """
        Summarize the provided response or extract a question if it contains one.

        Args:
            response (str): The response to be summarized or question to be extracted

        Returns:
            str: Summarized response or extracted question
        """

        logger.info("Starting summarize_response function")

        try:
            completion = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "system",
                        "content": "You are a question identifier and extractor.",
                    },
                    {"role": "user", "content": f"""Your task is to identify relevant question asked by the interviewer from the provided response. If you find any question in the response, return `question` with question along with its context, and `hasQuestion` as `true`. If no question is found, return `question` as `null`, and `hasQuestion` as `false`.\n\nINSTRUCTION:\n- Do not mention any name or company in the question.\n- Do not alter context of the question asked in the response.\n- Make sure the question contains all the relevant context, details, and/or scenarios.\n- Make sure the question is not vague.\n- Make sure to summarize the question in one sentence.\n- For any statement, remarks, or answers that are not questions, return `question` as null and `hasQuestion` as `false`.\n\nInterviewer Response:\n{response}"""},
                ],
                tools=[
                    {
                        "type": "function",
                        "function": {
                            "name": "ExtractQuestion",
                            "description": "Extracts question from the response if any, otherwise returns null.",
                            "parameters": QuestionSummary.model_json_schema(),
                        },
                    }
                ],
                tool_choice={
                    "type": "function",
                    "function": {"name": "ExtractQuestion"},
                },
                temperature=0.9
            )

            logger.info("Successfully executed summarize_response function")

            return json.loads(completion.choices[0].message.tool_calls[0].function.arguments)
        except Exception as e:
            logger.error(e)
            return e

    def requisition_processing_layer(self, requirements, industry):
        completion = self.client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "system",
                    "content": """
**Role:**  
You have been provided with a list of role requirements that need to be refined by eliminating any duplications or repetitions. The final version should be clear, well-structured, and focused on distinct, actionable skills and responsibilities. Additionally, the model should also extract binary requirements, which are core or fundamental qualifications that should be evaluated with a binary (True/False or Pass/Fail) method, such as non-negotiable educational background, required level of experience, or other critical prerequisites.

**Instructions:**

1. **Remove Redundancies:**  
   Identify and eliminate any duplicate or overly similar items. Retain only one version of any repeated skill, responsibility, or tool.

2. **Isolate Individual Requirements:**  
   Ensure that each requirement addresses only one specific skill or area of expertise. If multiple skills or tools are combined in one point, split them into separate, focused statements.

3. **Merge Similar Items:**  
   Combine closely related items into a single, clear requirement. Ensure that all unique details from the original entries are preserved in the merged statement.

4. **Preserve Key Details:**  
   Retain all unique technical skills, tools, responsibilities, and areas of expertise. Avoid removing essential details even if similar points are mentioned elsewhere.

5. **Select Exactly Ten Major Requirements:**  
   - Carefully analyze the refined list and prioritize the **most critical and impactful skills or responsibilities** for the role.  
    - Ensure the final output consists of **exactly ten distinct and actionable requirements** that represent the **most essential qualifications** of the role.  
    - These ten requirements must reflect the core technical and functional responsibilities necessary for success in the role.  
    - **Ensure Industry Relevance:** The final ten requirements should align closely with the specific **industry** of the role, focusing on qualifications and responsibilities essential to that domain.  
    - Omit qualifications that do not pertain directly to the role's technical requirements (e.g., degrees, years of experience) unless they specify essential expertise.
    - **Ensure Variety Across Core Areas:** The selected requirements should collectively represent a **breadth of key areas**, covering a variety of critical skills, tools, and responsibilities to ensure a balanced and well-rounded view of the role.  

6. **Identify Binary Requirements:**  
   - **Identify any core or fundamental requirement** that should be evaluated using a binary (True/False or Pass/Fail) method.  
   - These binary requirements typically involve non-negotiable criteria, such as mandatory educational qualifications, specific certifications, essential technical skills, or critical experience levels. 
   - **Note:** *Do not include any location-based requirements in this section.* 

7. **Maintain the Original Content and Wording:**
   - Do not change the wording of the original requirements unless necessary to eliminate redundancy or improve clarity.
   - **Do not rephrase** or alter the meaning of the original points unless required to split or merge items.
   - Ensure that the final list is both clear and focused on actionable skills and responsibilities.

**Objective:**  
Provide a list of major requirements that is clear, well-organized, and focused on key skills and responsibilities, with each requirement distinct and actionable, free from any duplication or repetition.  The final list must contain **no more than ten major requirements** that are critical for assessing the candidate in an interview. Additionally, the list should be detailed enough to provide a comprehensive understanding of each requirement and not overly concise. Each requirement should be described with sufficient specificity and depth to ensure clarity and alignment with the role's technical and functional expectations.

Additionally, make sure to provide a separate list of binary requirements, which include fundamental, non-negotiable qualifications (e.g., mandatory education, certifications, or critical experience) that should be assessed as True/False or Pass/Fail.
                """,
                },
                {
                    "role": "user",
                    "content": f"""Requirements: - {requirements}\n Industry: - {industry}""",
                },
            ],
            tools=[
                {
                    "type": "function",
                    "function": {
                        "name": "RequisitionProcessing",
                        "description": "Extracts a list of major technical and binary requirements, eliminating duplicates and redundancies, with each requirement focused on a distinct skill or expertise.",
                        "parameters": CleanedReq.model_json_schema(),
                    },
                }
            ],
            tool_choice={
                "type": "function",
                "function": {"name": "RequisitionProcessing"},
            },
            temperature=0,
        )
        return completion.choices[0].message.tool_calls[0].function.arguments

    def evaluation_criteria(self, technical_requirements: List[str], binary_requirements: List[str]) -> EvaluationCriteria:
        """
        Generates a detailed, actionable evaluation framework for assessing candidates based on the provided job description.

        Args:
            job_description (str): The job description to be analyzed for creating the evaluation framework.

        Returns:
            dict: A dictionary containing list of criteria. Each criterion includes the requirement, a detailed list of criterion to assess the candidate’s expertise and performance in meeting the specified requirement, a boolean indicating if the criterion requires a binary evaluation, and the weightage of the criterion out of 100.

        Raises:
            Exception: If there is an error in generating the evaluation criteria.
        """

        try:
            logger.info("Generating evaluation criteria")

            evaluation_criteria = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "system",
                        "content": self.system_prompt_evaluation_criteria,
                    },
                    {
                        "role": "user",
                        "content": f"""Technical Requirements:\n\n{technical_requirements}\n\nBinary Requirements:\n\n{binary_requirements}""",
                    }
                ],
                tools=[
                    {
                        "type": "function",
                        "function": {
                            "name": "EvaluationCriteria",
                            "description": "Extracted evaluation criteria",
                            "parameters": EvaluationCriteria.model_json_schema(),
                        },
                    }
                ],
                tool_choice={
                    "type": "function",
                    "function": {"name": "EvaluationCriteria"},
                },
            )

            return json.loads(evaluation_criteria.choices[0].message.tool_calls[0].function.arguments)
        except Exception as e:
            raise e

    def get_content_for_cold_email(
        self, messages, response_model, description=None, temperature=0.8
    ):
        try:
            if response_model:
                chat_response = self.client.chat.completions.create(
                    model="gpt-4o",
                    messages=messages,
                    temperature=0.4,
                    tools=[
                        {
                            "type": "function",
                            "function": {
                                "name": "ForEmailWriting",
                                "description": description,
                                "parameters": response_model.model_json_schema(),
                            },
                        }
                    ],
                    tool_choice={
                        "type": "function",
                        "function": {"name": "ForEmailWriting"},
                    },
                )
                response_model.model_validate_json(
                    chat_response.choices[0].message.tool_calls[0].function.arguments
                )
                print(
                    f"== LLM Response With Pydantic: {chat_response.choices[0].message.tool_calls[0].function.arguments} =="
                )
                parse_response = json.loads(
                    chat_response.choices[0].message.tool_calls[0].function.arguments
                )

            else:
                chat_response = self.client.chat.completions.create(
                    # model="meta-llama/Meta-Llama-3.1-8B-Instruct",
                    model="gpt-4o",
                    messages=messages,
                    temperature=temperature,
                )
                print(
                    f"== LLM Response Without Pydantic: {chat_response.choices[0].message.content} =="
                )
                parse_response = json.loads(
                    chat_response.choices[0].message.content)

            return parse_response
        except Exception as error:
            print(f"== LLM Error: {str(error)} ==")
            return None

    def get_embeddings(self, text):
        """
        Generates vector embeddings for the given text using GPT-4o embedding model.
        """
        try:

            response = self.client.embeddings.create(
                model="text-embedding-3-large",  # GPT-4o's latest embedding model
                input=text,
                dimensions=3072
            )

            # return response
            embedding_vector = response.data[0].embedding

            cost = (
                response.usage.total_tokens / 1000
            ) * self.embedding_cost_per_1000_tokens

            # logger.info(
            #     f"Generated embeddings for text: {text[:50]}..."
            # )  # Log first 50 chars
            return {"vector": embedding_vector, "cost": cost}

        except Exception as e:
            logger.error(f"Error generating embeddings: {e}")
            return None
        

    def get_profession(self,professions,job_title):
        try:
                logger.info("Getting profession started")
                completion = self.client.chat.completions.create(
                    model="gpt-4o",
                    messages=[
                            {
                                "role": "system",
                                "content": self.system_prompt_professions,
                            },
                            {
                                "role": "user",
                                "content": f"""Professions:\n\n{professions}\n\nJob Title:\n\n{job_title}""",
                            }
                        ],
                    tools=[
                        {
                            "type": "function",
                            "function": {
                                "name": "RequsiitonProfessions",
                                "description": "Extracted profession name",
                                "parameters": RequisitionProfession.model_json_schema(),
                            },
                        }
                    ],
                    tool_choice={
                        "type": "function",
                        "function": {"name": "RequsiitonProfessions"},
                    },
                    temperature=0,
                )
                logger.info("Getting profession completed")
                return json.loads(completion.choices[0].message.tool_calls[0].function.arguments)
        
        except Exception as e:
            logger.error(f"Error getting profession: {e}")
            raise e

    def map_recruiter_requisition(self, parsed_data):
        try:
            logger.info("Mapping recruiter requisition started")
            completion = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                        {
                            "role": "system",
                            "content": self.system_prompt_mapping_recruiter_requisition,
                        },
                        {
                            "role": "user",
                            "content": f"""Parsed Data:\n\n{parsed_data}""",
                        }
                    ],
                tools=[
                    {
                        "type": "function",
                        "function": {
                            "name": "MapRecruiterRequisition",
                            "description": "Mapped recruiter requisition details",
                            "parameters": RecruiterRequisitionCreateSchema.model_json_schema(),
                        },
                    }
                ],
                tool_choice={
                    "type": "function",
                    "function": {"name": "MapRecruiterRequisition"},
                },
                temperature=0,
            )
            logger.info("Mapping recruiter requisition completed")
            return json.loads(completion.choices[0].message.tool_calls[0].function.arguments)
        
        except Exception as e:
            logger.error(f"Error in mapping recruiter requisition: {e}")
            raise e

    def get_parsed_requisition_requirements(self, requisition):
        try:
            logger.info("Getting requirements for requisition started")
            completion = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                        {
                            "role": "system",
                            "content": self.system_prompt_requisition_requirements,
                        },
                        {
                            "role": "user",
                            "content": f"""Job Description:\n\n{requisition}""",
                        }
                    ],
                tools=[
                    {
                        "type": "function",
                        "function": {
                            "name": "RequisitionRequirements",
                            "description": "Extracts mandatory and preferred requirements from the job description",
                            "parameters": RequisitionRequirementsSchema.model_json_schema(),
                        },
                    }
                ],
                tool_choice={
                    "type": "function",
                    "function": {"name": "RequisitionRequirements"},
                },
                temperature=0,
            )
            logger.info("Getting requirements for requisition completed")
            return json.loads(completion.choices[0].message.tool_calls[0].function.arguments)
        
        except Exception as e:
            logger.error(f"Error getting requirements for the requisition: {e}")
            raise e


    def get_requisition_requirements_evaluation(self,resume,transcript,parsed_requirements):
        try:
            logger.info("Getting requisition requirements evaluation started")
            completion = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                        {
                            "role": "system",
                            "content": self.system_prompt_requirement_evaluation,
                        },
                        {
                            "role": "user",
                            "content": f"""Conversation Transcript:\n\n{transcript}\n\nResume:\n\n{resume}\n\nParsed Requirements:\n\n{parsed_requirements}""",
                        }
                    ],
                tools=[
                    {
                        "type": "function",
                        "function": {
                            "name": "RequisitionRequirementsEvaluation",
                            "description": "Requisition requirements evaluation based on the resume and transcript",
                            "parameters": 
                            RequisitionRequirementsEvaluationSchema.model_json_schema(),
                        },
                    }
                ],
                tool_choice={
                    "type": "function",
                    "function": {"name": "RequisitionRequirementsEvaluation"},
                },
                temperature=0,
            )
            logger.info("Getting requisition requirements evaluation completed")
            return json.loads(completion.choices[0].message.tool_calls[0].function.arguments)

        except Exception as e:
            logger.error(f"Error getting requisition requirements evaluation: {e}")
            raise e


    def parse_recruiter_requisition(self, transcript):
        try:
            logger.info("Parsing recruiter requisition started")
            completion = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                        {
                            "role": "system",
                            "content": self.system_prompt_recruiter_requisition,
                        },
                        {
                            "role": "user",
                            "content": f"""Conversation Transcript:\n\n{transcript}""",
                        }
                    ],
                tools=[
                    {
                        "type": "function",
                        "function": {
                            "name": "RecruiterRequisitionParsing",
                            "description": "Extracted recruiter requisition details",
                            "parameters": JobRequirementSchema.model_json_schema(),
                        },
                    }
                ],
                tool_choice={
                    "type": "function",
                    "function": {"name": "RecruiterRequisitionParsing"},
                },
                temperature=0,
            )
            logger.info("Parsing recruiter requisition completed")
            return json.loads(completion.choices[0].message.tool_calls[0].function.arguments)
        
        except Exception as e:
            logger.error(f"Error parsing recruiter requisition: {e}")
            raise e