import json
import logging
from exa_py import Exa
from exa_py.websets.types import CreateWebsetParameters, CreateEnrichmentParameters
from dependencies import EXA_API_KEY

logger = logging.getLogger(__name__)


class ExaServiceClass():
    def __init__(self):
        self.exa_client = Exa(EXA_API_KEY)

    def get_candidates(self, payload):
        try:
            webset = self.exa_client.websets.create(
                params=CreateWebsetParameters(
                    search={
                        "query": payload.query,
                        # "criteria": [
                        #     "professional experience as a python developer",
                        #     "at least 2 years of relevant work experience",
                        #     "currently based in dubai",
                        # ],
                        "count": 2,
                    },
                    enrichments=[
                        CreateEnrichmentParameters(
                            description="Email",
                            format="text",
                        ),
                    ],
                )
            )
            logger.info(f"Webset created with ID: {webset.id}")
            # Wait until Webset completes processing
            webset = self.exa_client.websets.wait_until_idle(webset.id)
            # Retrieve Webset Items
            items = self.exa_client.websets.items.list(webset_id=webset.id)
            return items
        except Exception as e:
            logger.error(f"Error in get_candidates: {str(e)}")
            raise e
