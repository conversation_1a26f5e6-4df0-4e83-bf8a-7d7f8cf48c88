from models.interview import Interview
from sqlalchemy.exc import SQLAlchemyError
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, BackgroundTasks
from dependencies import PWD_CONTEXT, INTERVIEW_DURATION
import random
import string
from custom_exceptions import EntityAlreadyExistsException, EntityNotFoundException
from models.user_requisition import UserRequisition
from fastapi.encoders import jsonable_encoder
from sqlalchemy import desc
from datetime import datetime
from services.notification.email import send_email_background
from services.requisition.requisition import RequisitionServiceClass
from helper.helper import (convert_utc_to_local)
from dependencies import APP_HOST, USER_INVITE_URL, MAIL_CONFIG,SALLY_INTERVIEW_v2_URL

from services.auth import AuthServiceClass

auth_service_object = AuthServiceClass()


class UserInterviewServiceClass:
    def __init__(self):
        self.allowed_date = datetime(2024, 10, 22)  # 22nd October 2024

    def __get_scheduled_interview(self, request, db):
        try:
            scheduled_interview = (
                db.query(Interview)
                .filter(
                    Interview.user_id == request.state.user.id,
                    Interview.status < Interview.INTERVIEW_RESCHEDULED,
                )
                .first()
            )
            return scheduled_interview
        except Exception as e:
            raise e

    def __get_rescheduled_interviews(self, request, db):
        try:
            rescheduled_interviews = (
                db.query(Interview)
                .filter(
                    Interview.user_id == request.state.user.id,
                    Interview.status == Interview.INTERVIEW_RESCHEDULED,
                )
                .all()
            )
            return rescheduled_interviews
        except Exception as e:
            raise e

    def get_user_interviews(self, request, db):
        try:
            scheduled_interview = None
            rescheduled_interviews = []
            completed_interview = []
            user_interviews = (
                db.query(Interview)
                .filter(
                    Interview.user_id == request.state.user.id,
                )
                .all()
            )
            for user_interview in user_interviews:
                if user_interview.status < Interview.INTERVIEW_RESCHEDULED:
                    scheduled_interview = user_interview
                if user_interview.status == Interview.INTERVIEW_RESCHEDULED:
                    rescheduled_interviews.append(user_interview)
                if user_interview.status > Interview.INTERVIEW_RESCHEDULED:
                    completed_interview.append(user_interview)
            return jsonable_encoder(
                {
                    "scheduled_interview": scheduled_interview,
                    "rescheduled_interviews": rescheduled_interviews,
                    "completed_interview": completed_interview,
                }
            )
        except Exception as e:
            raise e

    def schedule_user_interview(self, interview, request, db, background_tasks):
        try:
            existing_interview = (
                db.query(Interview)
                .filter(
                    Interview.user_id == request.state.user.id,
                    Interview.status < Interview.INTERVIEW_RESCHEDULED,
                )
                .first()
            )
            if existing_interview:
                raise EntityAlreadyExistsException(
                    "You already have an interview scheduled"
                )
            # TODO:: Need to check if user has applied for any requisition
            # Need to optimize this one
            user_requisition = (
                db.query(UserRequisition)
                .filter(
                    UserRequisition.user_id == request.state.user.id,
                    UserRequisition.is_applied == 1,
                )
                .first()
            )
            if not user_requisition:
                raise EntityNotFoundException(
                    "You haven't applied for any requisition")

            if interview.scheduled_at < self.allowed_date:
                raise ValueError(
                    "Interviews can only be scheduled on or after October 22nd.")

            if interview.requisition_id:
                user_requisition.requisition_id = interview.requisition_id
            user_interview = Interview(
                code=self.__generate_random_code(db),
                user_id=request.state.user.id,
                scheduled_at=interview.scheduled_at,
                time_zone=interview.time_zone,
                duration=INTERVIEW_DURATION,
                status=Interview.INTERVIEW_PENDING,
                profession_id=request.state.user.profession_id,
                requisition_id=user_requisition.requisition_id,
            )
            db.add(user_interview)
            db.commit()
            db.refresh(user_interview)

            # Convert UTC to local time
            converted_date_time = convert_utc_to_local(
                user_interview.scheduled_at)
            # Create access token to verify user
            secret_token = auth_service_object.create_access_token(
                request.state.user.email
            )
            requisition = RequisitionServiceClass().get_requisition(
                request, user_interview.requisition_id, db
            )
            # Email Body
            organization = requisition.get("organization")
            redirect_link = f"{APP_HOST}{USER_INVITE_URL}?access_token={secret_token}"
            email_body = {
                "name": f"{request.state.user.first_name} {request.state.user.last_name}",
                "job_title": requisition['title'],
                "date": converted_date_time["date"],
                "time": converted_date_time["time"],
                "time_zone": user_interview.time_zone,
                "redirect_link": redirect_link,
                "organization_name": organization['name'],
                "calendar_invite": True,
                "contact_email": MAIL_CONFIG["MAIL_FROM"],
                "app_name": MAIL_CONFIG["MAIL_FROM_NAME"],
            }
            # send email
            send_email_background(
                background_tasks,
                "Interview Scheduled – Get Ready to Shine!",
                request.state.user.email,
                email_body,
                "interview_scheduled.html",
            )

            return user_interview

        except Exception as e:
            raise e

    def reschedule_user_interview(self, id, interview, request, db, background_tasks):

        try:
            pervious_interview = (
                db.query(Interview)
                .filter(
                    Interview.id == id,
                    Interview.user_id == request.state.user.id,
                )
                .first()
            )

            if not pervious_interview:
                raise EntityNotFoundException(
                    f"No interview found with id {id}")

            pervious_interview.status = Interview.INTERVIEW_RESCHEDULED
            db.commit()
            db.refresh(pervious_interview)

            user_interview = self.schedule_user_interview(
                interview, request, db, background_tasks)
            return user_interview
        except SQLAlchemyError as e:
            raise e

        except Exception as e:
            raise e

    def __generate_random_code(self, db):
        length = 10
        characters = string.ascii_uppercase + string.digits

        # Repeat until a unique code is generated
        while True:
            # Generate a random code
            code = "".join(random.choice(characters) for _ in range(length))

            # Check if the code already exists in the UserRequisition table
            exists = db.query(Interview).filter(Interview.code == code).first()

            if not exists:
                # The code is unique; return it
                return code

    def get_recent_user_interview(self, request, db):
        try:
            completed_interviews = (
                db.query(Interview)
                .filter(
                    Interview.user_id == request.state.user.id,
                )
                .order_by(desc(Interview.id))
                .first()
            )
            return jsonable_encoder(completed_interviews)
        except Exception as e:
            raise e

    def get_recent_user_interview_no_auth(self, user_id, db):
        try:
            completed_interviews = (
                db.query(Interview)
                .filter(
                    Interview.user_id == user_id,
                )
                .order_by(desc(Interview.id))
                .first()
            )
            return jsonable_encoder(completed_interviews)
        except Exception as e:
            raise e

    def schedule_interview_with_sally(self, request, background_tasks: BackgroundTasks, email):
        try:
            secret_token = auth_service_object.create_access_token(
                request.state.user.email
            )
            if email == 'interview':
                redirect_link = f"{APP_HOST}{SALLY_INTERVIEW_v2_URL}?access_token={secret_token}"
                email_template = "interview_with_sally.html"
            else:
                redirect_link = f"{APP_HOST}{'user/upload-resume'}?access_token={secret_token}"
                email_template = "complete_your_resume.html" #interview template will be changed 

            email_body = {
                "first_name": request.state.user.first_name,
                "redirect_link": redirect_link
            }
            
            send_email_background(
                background_tasks,
                "Supercharge your job search with Sally",
               request.state.user.email,
                email_body,
                email_template,
            )
            return {
                "email" : request.state.user.email,
            }
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail="Failed to send email."
            )
