from bson import ObjectId
from typing import Dict
from helper.helper import calculate_experience_years
from models.organization import OrganizationDetail
from services.interview.interview import InterviewServiceClass
from schemas.user import *
from schemas.user_education import UserEducationCreateUpdate
from schemas.user_experience import UserExperienceCreateUpdate
from models.user import User
from models.school import School
from models.organization import Organization
from models.user_education import UserEducation
from models.user_experience import UserExperience
from models.user_experience_skill import UserExperienceSkill
from models.user_skill import UserSkill
from models.skill import Skill
from models.user_meta import UserMeta
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPException, status
from fastapi.encoders import jsonable_encoder
from dependencies import PWD_CONTEXT, JWT_TOKEN_EXPIRY_TIME,UNIVERSAL_PASSWORD
import json
from typing import List
from services.auth import AuthServiceClass
from custom_exceptions import EntityAlreadyExistsException, EntityNotFoundException
from services.skills.skills import SkillsServiceClass
from sqlalchemy.orm import  joinedload
import logging
from models.recruiter import Recruiter
from models.user_project import UserProject
from schemas.recruiter import Recruiter<PERSON><PERSON>
from schemas.user_project import UserProjectCreateUpdate
from services.project.project import ProjectService
from services.school import SchoolServiceClass
from types import SimpleNamespace
from datetime import timedelta
from services.organization.organization import OrganizationServiceClass

logger = logging.getLogger(__name__)

school_service = SchoolServiceClass()
organization_service = OrganizationServiceClass()

interview_service = InterviewServiceClass()

class UserServiceClass:
    def hash_password(self, password: str):
        return PWD_CONTEXT.hash(password)

    def register_user(self, request: PushToTalkUserCreate, db):
        try:
            auth_service_object = AuthServiceClass()
            user = db.query(User).filter(User.email == request.email).first()

            hashed_password = self.hash_password(UNIVERSAL_PASSWORD)

            if user:
                user.first_name = request.first_name
                user.last_name = request.last_name
                user.phone = request.phone
                user.password = hashed_password
                user.email = request.email
            else:
                user = User(
                    first_name=request.first_name,
                    last_name=request.last_name,
                    phone=request.phone,
                    email=request.email,
                    password=hashed_password,
                )
                db.add(user)

            db.commit()
            db.refresh(user)

            # Always generate a new token after create/update
            access_token = auth_service_object.create_login_access_token(
                data={"sub": user.email},
                expires_delta=JWT_TOKEN_EXPIRY_TIME
            )
            user.access_token = access_token

            return user

        except Exception as e:
            raise e
        
    def register_guest_user(self, request, db, mongodb_db):
        if not request.email:
            raise ValueError("Email is required for guest users.")

        existing_user = db.query(User).filter(User.email == request.email).first()
        if existing_user:
            raise EntityAlreadyExistsException("Email is already associated with an existing user.")

        guest_user = User(
            first_name=request.first_name,
            last_name=request.last_name,
            email=request.email,
            is_guest=True
        )
        db.add(guest_user)
        db.commit()
        db.refresh(guest_user)

        access_token = AuthServiceClass().create_login_access_token(
            data={"sub": guest_user.email},
            expires_delta=JWT_TOKEN_EXPIRY_TIME
        )

        return {"user": jsonable_encoder(guest_user), "access_token": access_token}




    def register_user_UNI(self, data, mongodb_db):
        email = data.email
        interview_subject = data.interview_subject
        name = data.name
        difficulty = data.difficulty

        if not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Email is required.")
        if not name:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Name is required.")
        if not interview_subject:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST,
                                detail="Interview subject is required.")

        collection = mongodb_db["university_interview"]
        existing_user = collection.find_one({"email": email})

        if existing_user:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST,
                                detail="User with email already exists, try another one.")

        user_info = f"Email is {email}, interview subject is {interview_subject} and name is {name}."
        current_prompt = f"""You are an interview preparation maker. Given user information, generate an interview question for the user. You are also given some additional previous context optionally. It contains the previous user information, and the questions and user answers provided. Make sure to ask different questions from the previous ones, even if user answers incorrectly. User info is: {user_info} Make sure to ask questions on a {difficulty} level. Previously asked question-answers are: """

        guest_user = {
            "name": name,
            "email": email,
            "interview_subject": interview_subject,
            "current_prompt": current_prompt
        }

        inserted_user = collection.insert_one(guest_user)
        guest_user["_id"] = str(inserted_user.inserted_id)

        return guest_user


    @staticmethod
    def collect_interview_prep_data(request: Dict, db):
        """
        Collects interview preparation data from the user and returns it as a response.

        Args:
            request (Dict): The request payload containing resume, job description, and job title.
            db: Database session (not used for now).

        Returns:
            Dict: The collected data.
        """
        try:
            # Extract data from the request
            resume = request.get("resume")
            job_description = request.get("job_description")
            job_title = request.get("job_title")

            # Return the data for now (without any processing)
            return {
                "resume": resume,
                "job_description": job_description,
                "job_title": job_title,
            }
        except Exception as e:
            raise Exception(f"Error while collecting interview prep data: {e}")


    def register_guest_user(self, request, db, mongodb_db):
        if not request.email:
            raise ValueError("Email is required for guest users.")

        existing_user = db.query(User).filter(User.email == request.email).first()
        if existing_user:
            raise EntityAlreadyExistsException("Email is already associated with an existing user.")

        guest_user = User(
            first_name="Guest",
            last_name="User",
            email=request.email,
            is_guest=True
        )
        db.add(guest_user)
        db.commit()
        db.refresh(guest_user)

        access_token = AuthServiceClass().create_login_access_token(
            data={"sub": guest_user.email},
            expires_delta=JWT_TOKEN_EXPIRY_TIME
        )

        return {"user": jsonable_encoder(guest_user), "access_token": access_token}

    def update_user(self, userData, request, db):
        try:
            user = db.query(User).filter(
                User.id == request.state.user.id).first()

            if userData.first_name is not None:
                user.first_name = userData.first_name
            if userData.last_name is not None:
                user.last_name = userData.last_name
            if userData.email is not None:
                user.email = userData.email
            if userData.profession_id is not None:
                user.profession_id = userData.profession_id
            if userData.phone is not None:
                user.phone = userData.phone
            if userData.expected_salary is not None:
                user.expected_salary = userData.expected_salary
            if userData.location_preferences is not None:
                user.location_preferences = userData.location_preferences
            if userData.notice_period is not None:
                user.notice_period = userData.notice_period
            if userData.nationality is not None:
                user.nationality = userData.nationality
            if userData.date_of_birth is not None:
                user.date_of_birth = userData.date_of_birth
            if userData.gender is not None:
                user.gender = userData.gender
            if userData.city_id is not None:
                user.city_id = userData.city_id

            db.add(user)
            db.commit()
            db.refresh(user)
            return user
        except Exception as e:
            raise e

    def complete_user_signup(self, userData, request, db):
        try:
            user = db.query(User).filter(
                User.id == request.state.user.id).first()

            user.expected_salary = userData.expected_salary
            user.location_preferences = userData.location_preferences
            user.notice_period = userData.notice_period
            if userData.linkedin_url is not None:
                user.linkedin_url = userData.linkedin_url
            db.add(user)
            db.commit()
            db.refresh(user)
            return user
        except Exception as e:
            raise e

    def store_interview_prep_data(self, userData, resume_response, sql_db, mongodb_db):
        try:
            # Fetch user information from the SQL database
            user = sql_db.query(User).filter(
                User.id == userData.user_id).first()

            if not user:
                raise ValueError(
                    f"User with ID {userData.user_id} not found in SQL database.")

            # print(userData)

            # Connect to the MongoDB collection
            collection = mongodb_db["user_data"]

            # Check if the user ID already exists in MongoDB
            existing_user_data = collection.find_one(
                {"user_id": userData.user_id})

            if existing_user_data:
                # Update fields except for the resume
                update_data = {
                    "job_title": userData.job_title,
                    "job_description": userData.job_description,
                }

                # Append the new resume to the existing list
                if isinstance(resume_response, list):
                    updated_resumes = existing_user_data.get(
                        "resume", []) + resume_response
                else:
                    updated_resumes = existing_user_data.get(
                        "resume", []) + [resume_response]

                # Update MongoDB entry
                collection.update_one(
                    # Query to find the document
                    {"user_id": userData.user_id},
                    {
                        "$set": update_data,         # Update the fields
                        # Ensure user_id is maintained
                        "$setOnInsert": {"user_id": userData.user_id},
                        # Append resumes, avoiding duplicates
                        "$addToSet": {"resume": {"$each": updated_resumes}},
                    }
                )
                return {"success": True, "message": "User data updated successfully."}

            else:
                # Create new entry if user doesn't exist
                user_data = {
                    "user_id": userData.user_id,
                    "job_title": userData.job_title,
                    "resume": resume_response if isinstance(resume_response, list) else [resume_response],
                    "job_description": userData.job_description,
                }
                result = collection.insert_one(user_data)
                return {"success": True, "inserted_id": str(result.inserted_id)}

        except Exception as e:
            raise Exception(f"Error storing interview prep data: {e}")

    def update_user_preferences(self, payload, request, db):
        try:
            user = db.query(User).filter(
                User.id == request.state.user.id).first()

            user.expected_salary = payload.expected_salary
            user.location_preferences = payload.loc_preferences
            user.notice_period = payload.notice_period
            user.salary_negotiable = payload.salary_negotiable

            db.add(user)
            db.commit()
            db.refresh(user)
            self.save_user_meta(request, payload, db)
            return user
        except Exception as e:
            raise e

        # so all i have to do a thing that add school name text field and while updating educatoin record we'll check whether school
        # exists if yes then return school id else create new school

    def update_education(
        self, educations: List[UserEducationCreateUpdate], request, db
    ):
        user_id = request.state.user.id
        user_education_response = []
        try:
            for education in educations:

                if hasattr(education, "id"):  # Check if ID is provided
                    # Try to find the existing record to update
                    user_education = (
                        db.query(UserEducation)
                        .filter(
                            UserEducation.id == education.id,
                            UserEducation.user_id == user_id,
                        )
                        .first()
                    )

                    if user_education:
                        user_education.school_id = education.school_id
                        user_education.degree = education.degree
                        user_education.field = education.field
                        user_education.description = education.description
                        user_education.grade = education.grade
                        # user_education.city_id = education.city_id
                        user_education.start_date = education.start_date
                        user_education.end_date = education.end_date
                        user_education.location=education.location
                    else:
                        # Handle the case where the record is not found (optional)
                        raise EntityNotFoundException(
                            f"UserEducation with id {education.id} not found."
                        )
                else:
                    # Create new record if no ID is provided
                    user_education = UserEducation(
                        user_id=user_id,
                        school_id=education.school_id,
                        degree=education.degree,
                        field=education.field,
                        description=education.description,
                        grade=education.grade,
                        city_id=0,
                        start_date=education.start_date,
                        end_date=education.end_date,
                        location=education.location
                    )
                    db.add(user_education)

                db.commit()
                db.refresh(user_education)
                user_education_response.append(user_education)

            return user_education_response

        except SQLAlchemyError as e:
            # db.rollback()
            raise e

        except Exception as e:
            # db.rollback()
            raise e

    def update_experience(
        self, experiences: List[UserExperienceCreateUpdate], request, db
    ):
        user_id = request.state.user.id
        user_experience_response = []
        try:
            for experience in experiences:

                if hasattr(experience, "id"):  # Check if ID is provided
                    # Try to find the existing record to update
                    user_experience = (
                        db.query(UserExperience)
                        .filter(
                            UserExperience.id == experience.id,
                            UserExperience.user_id == user_id,
                        )
                        .first()
                    )
                    if user_experience:
                        user_experience.organization_id = experience.organization_id
                        user_experience.job_title = experience.job_title
                        user_experience.emp_type = experience.emp_type
                        user_experience.description = experience.description
                        user_experience.from_date = experience.from_date
                        user_experience.to_date = experience.to_date
                        user_experience.city_id = 0
                        user_experience.location_type = experience.location_type
                        user_experience.location = experience.location

                    else:
                        # Handle the case where the record is not found (optional)
                        raise EntityNotFoundException(
                            f"User Experience with id {experience.id} not found."
                        )
                else:
                    user_experience = UserExperience(
                        user_id=user_id,
                        organization_id=experience.organization_id,
                        job_title=experience.job_title,
                        emp_type=experience.emp_type,
                        description=experience.description,
                        from_date=experience.from_date,
                        to_date=experience.to_date,
                        city_id=0,
                        location_type=experience.location_type,
                        location = experience.location
                    )
                    db.add(user_experience)

                db.commit()
                db.refresh(user_experience)
                user_experience_response.append(user_experience)
            return user_experience_response

        except Exception as e:
            raise e

    def update_project(
        self, projects: List[UserProjectCreateUpdate], request, db
    ) -> List[UserProject]:
        """
        Update or create user projects.
        This method updates existing user projects if an ID is provided in the
        `projects` list. If no ID is provided, it creates a new user project.
        The method commits the changes to the database and returns a list of
        updated or newly created user projects.

        Args:
            projects (List[UserProjectCreateUpdate]): A list of user project
            data to be updated or created.
            request: The request object containing the current user's state.
            db: The database session.

        Returns:
            List[UserProject]: A list of updated or newly created user projects.

        Raises:
            EntityNotFoundException: If a project ID is provided but the
            corresponding user project is not found.
            Exception: If any other exception occurs during the process.
        """

        user_id = request.state.user.id
        user_project_response = []
        project_service = ProjectService()

        try:
            for project in projects:
                if hasattr(project, "id"):  # Check if ID is provided
                    # Try to find the existing record to update
                    logger.info(f"Updating project with ID: {project['id']}")
                    user_project = project_service.get_project_by_id(
                        project['id'], request, db)
                    if user_project:
                        user_project = project_service.update_project(
                            project, request, db)
                    else:
                        # Handle the case where the record is not found (optional)
                        raise EntityNotFoundException(
                            f"User project with id {project['id']} not found."
                        )
                else:
                    logger.info(f"Creating new project for user ID: {user_id}")
                    user_project = ProjectService().create_project(project, request, db)

                user_project_response.append(user_project)
            return user_project_response
        except Exception as e:
            logger.error(
                f"Error creating or updating projects of user ID: {user_id}: {e}")
            raise e

    def __updateUserExperienceSkill(self, userExperience, skills, db):
        try:
            for skill in skills:
                user_experience_skill = (
                    db.query(UserExperienceSkill)
                    .filter(
                        UserExperienceSkill.user_experience_id == userExperience.id,
                        UserExperienceSkill.skill_id == skill,
                    )
                    .first()
                )
                if user_experience_skill:
                    continue
                else:
                    user_experience_skill = UserExperienceSkill(
                        user_experience_id=userExperience.id, skill_id=skill
                    )
                    db.add(user_experience_skill)
                    db.commit()
                    db.refresh(user_experience_skill)
            return True
        except Exception as e:
            raise e

    def update_user_resume_detail(self, user_object_to_update: dict, request, db):
        try:
            user = db.query(User).filter(
                User.id == request.state.user.id).first()
            user.about = user_object_to_update["about"]
            user.total_experience = user_object_to_update["total_experience"]
            user.resume_link = user_object_to_update["resume_link"]
            user.city_id = user_object_to_update["city_id"]
            user.country_id = user_object_to_update["country_id"]
            user.state_id = user_object_to_update["state_id"]
            user.location = user_object_to_update["location"]
            # if user.linkedin_url is None or user.linkedin_url.strip() == "":
            user.linkedin_url = user_object_to_update["linkedin"]
            db.add(user)
            db.commit()
            db.refresh(user)
            return user
        except Exception as e:
            raise e

    def update_user_profile_picture(self, profile_picture_link, request, db):
        """
        Update user's profile picture link in the database.

        Args:
            profile_picture_link (str): The link of the profile picture
            request (Request): The request object
            db (Session): The database session

        Returns:
            User: The updated user object
        """
        try:
            logger.info(
                f"Updating user profile picture link: {profile_picture_link}")
            # Get the user by ID
            user = db.query(User).filter(
                User.id == request.state.user.id).first()
            logger.info(profile_picture_link)
            # Update the user's profile picture link
            user.profile_pic = json.dumps(profile_picture_link)
            db.add(user)
            db.commit()
            db.refresh(user)
            logger.info(
                f"User profile picture link updated successfully: {user.profile_pic}")
            return user
        except Exception as e:
            raise e

    def get_user(self, request, db):
        try:
            user = db.query(User).filter(
                User.id == request.state.user.id).first()
            return user
        except Exception as e:
            raise e

    def delete_user(self, userEmail, request, db):
        try:
            user = db.query(User).filter(User.email == userEmail).first()
            if user:
                # Delete UserEducation records for the user
                db.query(UserEducation).filter(UserEducation.user_id == user.id).delete(
                    synchronize_session=False
                )
                user_experiences = (
                    db.query(UserExperience)
                    .filter(UserExperience.user_id == user.id)
                    .all()
                )

                # Extract all user_experience_ids
                user_experience_ids = [ue.id for ue in user_experiences]
                if user_experience_ids:
                    # Delete from UserExperienceSkill where user_experience_id is in the user_experience_ids list
                    db.query(UserExperienceSkill).filter(
                        UserExperienceSkill.user_experience_id.in_(
                            user_experience_ids)
                    ).delete(synchronize_session=False)
                    # Delete from UserExperience where user_id equals the given user_id
                    db.query(UserExperience).filter(
                        UserExperience.user_id == user.id
                    ).delete(synchronize_session=False)
                db.delete(user)
                db.commit()
            else:
                return False
            return True
        except Exception as e:
            db.rollback()
            raise e

    def create_update_user_skill(self, skills_array, request, db):
        try:
            skill_service = SkillsServiceClass()
            user_id = request.state.user.id
            added_skills = []
            for skill in skills_array:
                skill_object_for_service = {
                    "name": skill,
                    "description": None,
                }
                system_skill = skill_service.create_skills(
                    skill_object_for_service, db)
                user_skill = (
                    db.query(UserSkill)
                    .filter(
                        UserSkill.user_id == user_id,
                        UserSkill.skill_id == system_skill.id,
                    )
                    .first()
                )
                if not user_skill:
                    user_skill = UserSkill(
                        user_id=user_id, skill_id=system_skill.id)
                    db.add(user_skill)
                    db.commit()
                    db.refresh(user_skill)
                    added_skills.append(system_skill.name)
            return jsonable_encoder(added_skills)
        except Exception as e:
            raise e

    def get_user_detail(self, user_id, db):
        try:
            user = db.query(User).options(
                joinedload(User.educations).joinedload(UserEducation.school),
                joinedload(User.experiences).joinedload(
                    UserExperience.organization),
                joinedload(User.skills).joinedload(UserSkill.skill)
            ).filter(User.id == user_id).first()

            if not user:
                raise HTTPException(status_code=404, detail="User not found")

            return jsonable_encoder(user)

        except Exception as e:
            raise e

    def get_user_details(self, request, db):
        try:
            user = self.get_user(request, db)
            user_education = (
                db.query(
                    UserEducation.degree.label("degree"),
                    UserEducation.field.label("field"),
                    UserEducation.start_date.label("start_date"),
                    UserEducation.end_date.label("end_date"),
                    UserEducation.location.label("location"),
                    School.name.label("school"),
                )
                .join(School, UserEducation.school_id == School.id, isouter=True)
                .filter(UserEducation.user_id == user.id)
                .all()
            )
            user_experience = (
                db.query(
                    UserExperience.job_title.label("job_title"),
                    UserExperience.emp_type.label("emp_type"),
                    UserExperience.from_date.label("from_date"),
                    UserExperience.to_date.label("to_date"),
                    UserExperience.location.label("location"),
                    Organization.name.label("organization"),
                )
                .join(
                    Organization,
                    UserExperience.organization_id == Organization.id,
                    isouter=True,
                )
                .filter(UserExperience.user_id == user.id)
                .all()
            )

            user_skills = db.query(Skill.name.label("name")).join(
                UserSkill, Skill.id == UserSkill.skill_id).filter(UserSkill.user_id == user.id).all()

            user_education = [dict(zip(("degree", "field", "start_date", "end_date",
                                        "location", "school"), result)) for result in user_education]

            user_experience = [
                dict(
                    zip(
                        (
                            "job_title",
                            "emp_type",
                            "from_date",
                            "to_date",
                            "location",
                            "organization",
                        ),
                        result,
                    )
                )
                for result in user_experience
            ]

            user_skills = [result[0] for result in user_skills]

            return {
                "education": user_education,
                "experience": user_experience,
                "skills": user_skills,
            }
        except Exception as e:
            raise e

    def register_recruiter_user(self, request, db):
        try:
            auth_service_object = AuthServiceClass()
            existing_user = db.query(Recruiter).filter(
                Recruiter.email == request.email).first()
            if existing_user:
                raise EntityAlreadyExistsException(
                    "Recruiter with this email already exists"
                )
            hashed_password = self.hash_password(request.password)
            user = Recruiter(
                first_name=request.first_name,
                last_name=request.last_name,
                phone=request.phone,
                email=request.email,
                password=hashed_password,
                organization_id=0,  # To avoid null conflict
            )
            db.add(user)
            db.commit()
            db.refresh(user)
            access_token = auth_service_object.create_login_access_token(
                data={"sub": user.email}, expires_delta=JWT_TOKEN_EXPIRY_TIME
            )
            user.access_token = access_token
            if not user:
                raise Exception("Unable to register user")
            return user
        except Exception as e:
            raise e

    def get_user_by_id(self, user_id, db):
        """
        Get a user by user ID.

        Args:
            user_id (int): The ID of the user to retrieve.
            db (Session): The database session to use.

        Returns:
            User: The user object, or None if not found.
        """

        try:
            user = db.query(User).filter(User.id == user_id).first()
            return user
        except Exception as e:
            raise e

    def register_user_revamp(self, request: UserCreateRevamp, db):
        try:
            auth_service_object = AuthServiceClass()
            existing_user = db.query(User).filter(
                User.email == request.email).first()
            if existing_user:
                raise EntityAlreadyExistsException(
                    "User with this email already exists"
                )
            hashed_password = self.hash_password(request.password)
            user = User(
                first_name='',
                last_name='',
                # phone=request.phone,
                email=request.email,
                password=hashed_password,
            )
            db.add(user)
            db.commit()
            db.refresh(user)
            access_token = auth_service_object.create_login_access_token(
                data={"sub": user.email}, expires_delta=JWT_TOKEN_EXPIRY_TIME
            )
            user.access_token = access_token
            if not user:
                raise Exception("Unable to register user")
            return user
        except Exception as e:
            raise e

    def save_user_meta(self, request, payload, db):
        try:
            user_meta = UserMeta(
                user_id=request.state.user.id, key=payload.key, value=payload.value
            )
            db.add(user_meta)
            db.commit()
            db.refresh(user_meta)
            return jsonable_encoder(user_meta)
        except Exception as e:
            raise e

    def delete_education(self, request, education_id, db):
        try:
            user_education = db.query(UserEducation).filter(
                UserEducation.id == education_id, UserEducation.user_id == request.state.user.id).first()
            if user_education:
                db.delete(user_education)
                db.commit()
            else:
                raise EntityNotFoundException(
                    f"Education with id {education_id} not found.")
            return True
        except Exception as e:
            raise e

    def delete_experience(self, request, experience_id, db):
        try:
            user_experience = db.query(UserExperience).filter(
                UserExperience.id == experience_id, UserExperience.user_id == request.state.user.id).first()
            if user_experience:
                db.delete(user_experience)
                db.commit()
            else:
                raise EntityNotFoundException(
                    f"Experience with id {experience_id} not found.")
            return True
        except Exception as e:
            raise e

    def delete_user_skill(self, request, skill_id, db):
        try:
            user_id = request.state.user.id
            skill = db.query(Skill).filter(Skill.id == skill_id).first()
            if skill:
                user_skill = (
                    db.query(UserSkill)
                    .filter(
                        UserSkill.user_id == user_id,
                        UserSkill.skill_id == skill_id,
                    )
                    .first()
                )
                if user_skill:
                    db.delete(user_skill)
                    db.delete(skill)
                    db.commit()
                else:
                    raise EntityNotFoundException(
                        f"Skill with id {skill_id} not found.")
            return True
        except Exception as e:
            raise e

    def add_education(self, request, education: UserEducationCreateUpdate, db):
        try:
            user_id = request.state.user.id
            existing_educatoin = (
                db.query(UserEducation)
                .filter(
                    UserEducation.id == education.id,
                    UserEducation.user_id == user_id,
                )
                .first()
            )

            if existing_educatoin:
                raise EntityAlreadyExistsException("Education already exists")

            if education.school_name is not None and education.school_name != "":
                school_input = {
                    "name": education.school_name,
                    "logo": None,
                    "city_id": education.city_id,
                }
                # Convert to object
                school_input = SimpleNamespace(**school_input)
                school_object = school_service.add_school(school_input, db)
                school_id = school_object.id

            user_education = UserEducation(
                user_id=user_id,
                school_id=school_id,
                degree=education.degree,
                field=education.field,
                description=education.description,
                grade=education.grade,
                city_id=education.city_id,
                start_date=education.start_date,
                end_date=education.end_date,
            )
            db.add(user_education)
            db.commit()
            db.refresh(user_education)
            return jsonable_encoder(user_education)
        except Exception as e:
            raise e

    def add_experience(self, request, experience: UserExperienceCreateUpdate, db):
        try:
            user_id = request.state.user.id
            existing_experience = (
                db.query(UserExperience)
                .filter(
                    UserExperience.id == experience.id,
                    UserExperience.user_id == user_id,
                )
                .first()
            )

            if existing_experience:
                raise EntityAlreadyExistsException("Education already exists")
            if experience.organization_name is not None and experience.organization_name != "":
                organization_input = {
                    "name": experience.organization_name,
                    "logo": None,
                    "city_id": experience.city_id,
                }
                # Convert to object
                # organization_input = SimpleNamespace(**organization_input)
                organization_object = organization_service.add_organization(
                    organization_input, db)
                organization_id = organization_object.id

            user_experience = UserExperience(
                user_id=user_id,
                organization_id=organization_id,
                job_title=experience.job_title,
                emp_type=experience.emp_type,
                description=experience.description,
                from_date=experience.from_date,
                to_date=experience.to_date,
                city_id=experience.city_id,
                location_type=experience.location_type,
            )
            db.add(user_experience)

            db.commit()
            db.refresh(user_experience)
            return jsonable_encoder(user_experience)
        except Exception as e:
            raise e
    def insert_single_user_to_opensearch(self, user_id, db):
        try:
            user_data = interview_service.convert_data(user_id, db)
            if user_data:
                interview_service.insert_record_to_opensearch(user_data, db)
                db.query(User).filter(User.id == user_id).update(
                    {"opensearch_status": 1}, synchronize_session=False
                )
                db.commit()
        except Exception as e:
            logger.error(f"Error processing user_id={user_id}: {e}")
            db.rollback()
        logger.info("Inserted single user to opensearch and updating opensearch status to 1 in postgres")
        
        return True
    

    def get_user_experience_list(self, user_id, db):
        logger.info("Get user experiences list from db to update in opensearch")
        """
        Fetch and return the work experience list for a given user ID.
        """

        work_experiences = db.query(
            Organization.name.label("organization"),
            OrganizationDetail.description.label("organization_description"),
            UserExperience.job_title,
            UserExperience.location_type,
            UserExperience.from_date,
            UserExperience.to_date,
            UserExperience.emp_type,
            UserExperience.description.label("experience_description")
        ).join(
            Organization, UserExperience.organization_id == Organization.id
        ).outerjoin(
            OrganizationDetail, Organization.id == OrganizationDetail.organization_id
        ).filter(
            UserExperience.user_id == user_id
        ).all()

        experience_list = []
        for exp in work_experiences:
            experience_list.append({
                "organization": exp.organization or "",
                "job_title": exp.job_title or "",
                "location_type": exp.location_type or "",
                "from_date": exp.from_date,
                "to_date": exp.to_date,
                "emp_type": exp.emp_type or "",
                "description": exp.experience_description or "",
                "relevant_experience": calculate_experience_years(exp.from_date, exp.to_date),
                "about_company": exp.organization_description or "",
            })

        return experience_list


        try:
            print("hello world")
            """
                so we need to check first whether the user exists, if exists then update its details, if it doesn't exist
                then create the new user, simple dimple
            """
            auth_service_object = AuthServiceClass()
            existing_user = db.query(User).filter(
                User.email == request.email).first()
            if existing_user:
                #update the user details
                existing_user.first_name = request.first_name
                existing_user.last_name = request.last_name
                existing_user.email = request.email
                hashed_password = self.hash_password(request.password)
                existing_user.password = hashed_password


            hashed_password = self.hash_password(request.password)
            user = User(
                    first_name=request.first_name,
                    last_name=request.last_name,
                    email=request.email,
                    password=hashed_password,
                )
            db.add(user)
            db.commit()
            db.refresh(user)
            access_token = auth_service_object.create_login_access_token(
                data={"sub": user.email}, expires_delta=JWT_TOKEN_EXPIRY_TIME
            )
            user.access_token = access_token
            if not user:
                raise Exception("Unable to register user")
            return user

        except Exception as e:
            raise e
