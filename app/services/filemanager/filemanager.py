from datetime import datetime
import os
from fastapi import HTTPException
import hashlib
import boto3
from dependencies import (
    AWS_ACCESS_KEY_ID,
    AWS_SECRET_ACCESS_KEY,
    AWS_DEFAULT_REGION,
    AWS_BUCKET,
    AWS_URL,
    AWS_RESUME_FOLDER,
)
import logging
import shutil

from botocore.exceptions import NoCredentialsError, ClientError
import ffmpeg
from urllib.parse import urlparse

logger = logging.getLogger(__name__)


class FileManagerClass:
    def __init__(self):
        self.boto3_client = boto3.client(
            "s3",
            aws_access_key_id=AWS_ACCESS_KEY_ID,
            aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
            region_name=AWS_DEFAULT_REGION,
        )
        self.bucket_name = AWS_BUCKET
        self.temp_path = "storage/temp/"

    def save_file(self, request, file, path, file_type):
        try:
            hash_key = (
                str(request.state.user.id)
                + "-"
                + str(file.filename)
                + "-"
                + str(datetime.now())
                + "-"
                + file_type
            )
            # # create hash of file
            file_hash = self.__create_file_hash__(hash_key)
            # # get extension of file
            _, file_extension = os.path.splitext(file.filename)

            file_hash_with_extension = file_hash + file_extension

            self.boto3_client.upload_fileobj(
                file.file,
                self.bucket_name,
                path + file_hash_with_extension,
                ExtraArgs={"ACL": "public-read"},
            )

            return {
                "file_name": file.filename,
                "file_path": AWS_URL + path + file_hash_with_extension,
            }
        except Exception as e:
            raise e
        

    def __create_file_hash__(self, string):
        encoded_string = string.encode()
        hash_object = hashlib.sha256(encoded_string)
        hash = hash_object.hexdigest()
        return hash

    def save_temp_file(self, file):
        try:
            hash_key = str(file.filename) + "-" + str(datetime.now())
            if not os.path.exists(self.temp_path):
                os.makedirs(self.temp_path)

            # create hash of file
            file_hash = self.__create_file_hash__(hash_key)
            # get extension of file
            _, file_extension = os.path.splitext(file.filename)
            # store path of file
            file_path = os.path.join(self.temp_path, file_hash + file_extension)
            # store file
            with open(file_path, "wb") as fileObject:
                content = file.file.read()
                fileObject.write(content)

            return {"filePath": file_path}
        except Exception as e:
            raise e

    def delete_temp_file(self, file_path):
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception as e:
            raise e
        
    def delete_temp_dir(self,dir_path,file_path):
        """
        Cleanup temporary files and folders created during the intro video generation process.

        Args:
            download_folder (str, optional): The folder where the downloaded videos are stored. Defaults to "downloaded_videos".
            output_file (str, optional): The path to the generated intro video file. Defaults to "intro_video.mp4".

        Returns:
            bool: True if the cleanup is successful, False otherwise.
        """
        try:
            # Remove the download folder and its contents
            if os.path.exists(dir_path):
                shutil.rmtree(dir_path)
                logger.info(f"Deleted folder: {dir_path}")
            
            self.delete_temp_file(file_path)
            logger.info(f"Deleted file: {file_path}")
            
            logger.info("Temporary files cleanup completed successfully.")
            return True
        except Exception as e:
            logger.error(f"Error in cleanup_temp_files: {e}")
            return False

    def delete_s3_directory(self, path):
        try:
            objects = self.boto3_client.list_objects_v2(Bucket=self.bucket_name, Prefix=path)
            if "Contents" in objects:
                for obj in objects["Contents"]:
                    self.boto3_client.delete_object(Bucket=self.bucket_name, Key=obj["Key"])
            return True
        except Exception as e:
            print("Error in deleting s3 directory", e)
            raise e

    def get_s3_contents(self, path):
        try:
            objects = self.boto3_client.list_objects_v2(Bucket=self.bucket_name, Prefix=path)
            if 'Contents' not in objects:
                print("No files found in the specified folder.")
                return []

            video_files = []
            objects["Contents"].sort(key=lambda obj: obj["LastModified"])

            for obj in objects['Contents']:
                file_key = obj['Key']
                file_name = os.path.basename(file_key)
                video_files.append(AWS_URL + path + file_name)

            return video_files
        except Exception as e:
            raise e


    def get_s3_resume(self, path):
        try:
            logger.info(f"Path: {path}")
            # Retrieve the object metadata for the single file from S3
            obj = self.boto3_client.head_object(Bucket=self.bucket_name, Key=path)
            
            # If the object exists, construct the file URL
            if obj:
                resume_file_url = AWS_URL + path
                return resume_file_url
            else:
                logger.info(f"No resume file found at the specified path.")
                return None
        except Exception as e:
            raise e

    def get_s3_content_via_url(self, url):
        try:
            logger.info(f"URL: {url}")
            parsed_url = urlparse(url)
            key = parsed_url.path.lstrip('/')
            obj = self.boto3_client.head_object(Bucket=self.bucket_name, Key=key)
            if obj:
                return url
            else:
                return None
        except ClientError as e:
            if e.response['Error']['Code'] == "404":
                logger.warning(f"File not found in S3: {url}")
                return None
            else:
                logger.error(f"Error in get_s3_content_via_url: {e}")
                return None
        except Exception as e:
            logger.error(f"Unexpected error in get_s3_content_via_url: {e}")
            return None

    def copy_s3_file(self,source_bucket, source_key, destination_bucket, destination_key,user_id):

        try:
            logger.info(f"Check if file exists in source bucket for user ID: {user_id}")
            try:
                self.boto3_client.head_object(Bucket=source_bucket, Key=source_key)
                logger.info("File exists in source bucket, proceeding with copy operation.")
            except ClientError as e:
                if e.response['Error']['Code'] == "404":
                    logger.error("File does not exist in the source bucket.")
                    return None
                else:
                    logger.error(f"Unexpected error during file existence check: {e}")
                    return None
            logger.info(f"Copy S3 file started for user ID: {user_id}")
            copy_response = self.boto3_client.copy_object(
                Bucket=destination_bucket,
                CopySource={'Bucket': source_bucket, 'Key': source_key},
                Key=destination_key,
                ACL= 'public-read',
            )

            file_url = f"https://{destination_bucket}.s3.amazonaws.com/{destination_key}"
            logger.info(f"Destination File URL: {file_url}")
            logger.info(f"Copy S3 file completed")
            return file_url,copy_response

        except NoCredentialsError:
            logger.error("No AWS credentials found.")
            return None

        except ClientError as e:
            logger.error(f"Error occurred while copying file: {e}")
            return None
        except Exception as e:
            logger.error(f"Error occurred while copying file: {e}")
            return None

    def get_video_duration(self, s3_url):
        try:
            # Parse the S3 URL
            parsed_url = urlparse(s3_url)
            key = parsed_url.path.lstrip('/')

            logger.info(
                f"url: {s3_url} parsed_url: {parsed_url} key: {key}"
            )

            # Generate a presigned URL for the S3 object
            presigned_url = self.boto3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': AWS_BUCKET, 'Key': key},
                ExpiresIn=3600  # URL expiration in seconds
            )
            # Use ffprobe to get video duration from the presigned URL
            probe = ffmpeg.probe(presigned_url)
            duration = float(probe['format']['duration'])

            return duration
        except Exception as e:
            raise e

    def read_s3_file(self, s3_url):
        try:
            logger.info(f"Reading file from S3: {s3_url}")
            # Parse the S3 URL
            parsed_url = urlparse(s3_url)
            key = parsed_url.path.lstrip("/")  # remove leading '/'
            filename = os.path.basename(key)

            # Get file extension
            ext = os.path.splitext(filename)[1].lower()

            # Initialize S3 client
            s3 = boto3.client("s3")
            # Fetch the object
            response = s3.get_object(Bucket=self.bucket_name, Key=key)
            # Read the content
            content = (
                response["Body"].read()
            )  # change to 'rb' or another encoding if needed
            return {"content": content, "extension": ext}
        except Exception as e:
            logger.error(f"Unexpected error in read_s3_file: {e}")
            return None
