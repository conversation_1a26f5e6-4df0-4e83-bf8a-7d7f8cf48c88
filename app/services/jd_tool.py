

import uuid
from threading import Thread
from pymongo.errors import PyMongoError
from typing import List, Dict, Optional
from models.mongodb_models.questions import Question
from models.user import User
from pymongo.errors import PyMongoError
from services.external.openai import OpenAIServiceClass
from uuid import uuid4
import time
from bson import ObjectId
from fastapi import HTTPException

openai_service = OpenAIServiceClass()

class JDService:
    """Service layer for managing job description workflows."""

    def guardrail(self, request, userData):
        try:
            answer = userData.answer
            question = userData.question
            res = openai_service.check_guardrail_jd_tool(context={"answer": answer, "question":question})
            return res   
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))


    def get_jds(self, request, sql_db, mongodb_db):
        try:
            # Fetch user information from the SQL database
            user = sql_db.query(User).filter(User.id == request.state.user.id).first()

            if not user:
                raise HTTPException(status_code=404, detail=f"User with ID {request.state.user.id} not found in SQL database.")

            # Connect to the MongoDB collection
            collection = mongodb_db["job_description_tool"]

            # Find all documents with the user_id
            documents = collection.find({"user_id": request.state.user.id})

            # Extract the "_id" and "original_jd" key from each document
            original_jds = [{"_id": str(doc["_id"]), "original_jd": doc.get("original_jd")} for doc in documents if "original_jd" in doc]

            return original_jds

        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    def generate_kpis(self, request, data, sql_db, mongodb_db):
        try:
            # Fetch user information from the SQL database
            user = sql_db.query(User).filter(User.id == request.state.user.id).first()

            if not user:
                raise HTTPException(status_code=404, detail=f"User with ID {request.state.user.id} not found in SQL database.")

            # Connect to the MongoDB collection
            collection = mongodb_db["job_description_tool"]

            # Extract input data
            about_company = data.about_company
            role_title = data.role_title
            experience = data.experience
            location = data.location
            salary_range = data.salary_range
            stakeholders = data.stakeholders

  
            # Call the OpenAI service to generate KPIs
            response = openai_service.generate_kpis(context={
                "about_company": about_company,
                "role_title": role_title,
                "experience": experience,
                "job_location": location,
                "salary_range": salary_range,
                "stakeholders": stakeholders
            })

            kpis = response.get("kpis", [])

            # Create a new document with user and KPI information
            document = {
                "user_id": request.state.user.id,
                "email": user.email,
                "about_company": about_company,
                "role_title": role_title,
                "experience": experience,
                "location": location,
                "salary_range": salary_range,
                "stakeholders": stakeholders,
                "KPIs": kpis,
                "job_descriptions": []  # Empty list for job descriptions
            }

            # Insert the document into the collection
            result = collection.insert_one(document)

            # Return the KPIs and document ID
            return {"_id": str(result.inserted_id), "kpis": kpis}

        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))


    
    def generate_job_description(self, request, document_id, selected_kpis, mongodb_db):
        try:
            # Connect to the MongoDB collection
            collection = mongodb_db["job_description_tool"]

            # Fetch the document by ID
            document = collection.find_one({"_id": ObjectId(document_id)})

            if not document:
                raise HTTPException(
                    status_code=404,
                    detail=f"Document with ID {document_id} not found in MongoDB."
                )

            # Extract necessary fields from the document
            about_company = document.get("about_company")
            role_title = document.get("role_title")
            experience = document.get("experience")
            location = document.get("location")
            salary_range = document.get("salary_range")
            stakeholders = document.get("stakeholders")

            # Call the OpenAI service to generate the job description
            context = {
                "about_company": about_company,
                "role_title": role_title,
                "experience": experience,
                "job_location": location,
                "salary_range": salary_range,
                "stakeholders": stakeholders,
                "kpis": selected_kpis
            }

            job_description_response = openai_service.generate_job_description(context=context)

            # Create the original job description dictionary
            original_jd = {
                "id": str(uuid.uuid4()),  # Generate a unique ID
                "job_description": job_description_response['job_description'],
                "version": 1  # Set initial version as 1
            }

            # Update the document in MongoDB with the original job description
            collection.update_one(
                {"_id": ObjectId(document_id)},
                {"$set": {"original_jd": original_jd}}
            )

            # Return the new job description stored under "original_jd"
            return original_jd

        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))


    def get_updated_description(self, user_id: int, document_id: str, job_description_id: str, mongodb_db):
        """
        Retrieve a specific updated job description from MongoDB.
        """
        try:
            # Connect to the MongoDB collection
            collection = mongodb_db["job_description_tool"]

            # Find the document with the given _id and user_id
            document = collection.find_one({"_id": ObjectId(document_id), "user_id": user_id})
            
            if not document:
                raise HTTPException(
                    status_code=500,
                    detail="Document not found for the given user_id and document_id."
                )

            # Search for the job description with the given job_description_id in "updated_jd"
            job_description = next(
                (jd for jd in document.get("job_descriptions", []) if jd["id"] == job_description_id),
                None
            )

            if not job_description:
                raise HTTPException(status_code=500, detail="Job description not found.")

            return job_description

        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))


    def get_original_description(self, user_id: int, document_id: str, mongodb_db):
        """
        Retrieve the original job description from MongoDB.
        """
        try:
            # Connect to the MongoDB collection
            collection = mongodb_db["job_description_tool"]

            # Find the document with the given _id and user_id
            document = collection.find_one({"_id": ObjectId(document_id)})
            
            if not document:
                raise HTTPException(
                    status_code=404,
                    detail="Document not found for the given user_id and document_id."
                )

            # Retrieve the original job description
            original_jd = document.get("original_jd")

            if not original_jd:
                raise HTTPException(status_code=404, detail="Original job description not found.")

            return original_jd

        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    def update_job_description(self, request, data, mongodb_db):
        try:
            # Extract user ID and document ID from the request
            user_id = request.state.user.id
            document_id = data.obj_id  # Assuming document_id is passed in the request

            # Connect to the MongoDB collection
            collection = mongodb_db["job_description_tool"]

            # Find the document with the matching user_id and _id
            document = collection.find_one({
                "user_id": user_id,
                "_id": ObjectId(document_id)  # Convert string _id to ObjectId
            })

            if not document:
                raise HTTPException(status_code=404, detail=f"Document with ID {document_id} for user ID {user_id} not found in MongoDB.")


            # Extract the existing job descriptions
            job_descriptions = document.get("job_descriptions", [])

            # Get the latest version number
            latest_version = job_descriptions[-1]["version"] if job_descriptions else 0

            # Create a new job description dictionary
            new_job_description = {
                "id": str(uuid.uuid4()),  # Generate a unique ID
                "job_description": data.job_description,  # Get the new job description from the request
                "version": latest_version + 1  # Increment the version
            }
            

            # Append the new job description to the list
            job_descriptions.append(new_job_description)

            # Update the document in MongoDB
            collection.update_one(
                {"_id": ObjectId(document_id)},
                {"$set": {"job_descriptions": job_descriptions}}
            )

            # Return the new job description
            return new_job_description

        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))


    def get_text_and_audio(self, request):
        try:
            
            static_msgs = [
                "Hello, my name is Sally. I'm here to help you with your job description. This will only take a few minutes. What is the role title?",
                "What is your company's name and what does it do?",
                "How many years of experience do you require for this role?", 
                "What is the job location?", 
                "What is the salary range for this role?",
                "What are the key stakeholders that the candidate will work with or report to?",
                "For a role like this, usually these are the KPIs. Choose from these KPIs or you can add a custom KPI."
            ]

            audio_urls = [
                'https://snabup-staging.s3.ap-south-1.amazonaws.com/jd_audio/q1.mp3',
                'https://snabup-staging.s3.ap-south-1.amazonaws.com/jd_audio/q2.mp3',
                'https://snabup-staging.s3.ap-south-1.amazonaws.com/jd_audio/q3.mp3',
                'https://snabup-staging.s3.ap-south-1.amazonaws.com/jd_audio/q4.mp3',
                'https://snabup-staging.s3.ap-south-1.amazonaws.com/jd_audio/q5.mp3',
                'https://snabup-staging.s3.ap-south-1.amazonaws.com/jd_audio/q6.mp3',
                'https://snabup-staging.s3.ap-south-1.amazonaws.com/jd_audio/q7.mp3'
            ]

            return {"texts": static_msgs, "audio_urls": audio_urls}

            # audios = []
            # audios.extend([openai_service.generate_speech(msg) for msg in static_msgs])

            # return {"text":static_msgs, "audio":audios}


            
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
            # Return the updated job descriptions
            return {"job_descriptions": job_descriptions}
        
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

