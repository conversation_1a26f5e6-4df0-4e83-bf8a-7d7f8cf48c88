from services.external.opensearch import OpenSearchServiceClass
from dependencies import (
    OPENSEARCH_REQUISITION_EMBEDDING_INDEX,
    OPENSEARCH_RC_INDEX,
    RECOMMENDED_CANDIDATE_THRESHOLD_SCORE
)
import logging

import numpy as np
from services.requisition.requisition import RequisitionServiceClass
from models.requisition import Requisition

from services.external.openai import OpenAIServiceClass

logger = logging.getLogger(__name__)
requisition_service_object = RequisitionServiceClass()
open_ai_service_object = OpenAIServiceClass()

logger = logging.getLogger(__name__)

class CandidateRecommendationService:
    def get_similarity(self,req_id, db):
        try:
            logger.info(f"Getting recommended candidates for req_id started: {req_id}")

            requisition_data = db.query(Requisition).filter(Requisition.id == req_id).first()
            if requisition_data is None:
                logger.error(f"No requisition found with id: {req_id}")
                return {"error": "Requisition not found"}
            
            level = requisition_data.level
            if level is None:
                logger.error(f"No level found for requisition id: {req_id}")
                return {"error": "Level not found"}
            
            # Normalize the level to lowercase and remove extra spaces
            normalized_level = level.strip().lower()
            
            exp_greater_than_or_equals = ""
            exp_less_than_or_equals = ""
            if normalized_level in ["junior level", "junior-level"]:
                exp_greater_than_or_equals = 1
                exp_less_than_or_equals = 2
            elif normalized_level in ["mid level", "mid-level"]:
                exp_greater_than_or_equals = 2
                exp_less_than_or_equals = 3
            elif normalized_level == "senior level":
                exp_greater_than_or_equals = 3
                exp_less_than_or_equals = 4
            elif normalized_level == "manager/lead level": 
                exp_greater_than_or_equals = 5
                exp_less_than_or_equals = None
            else:
                logger.error(f"Invalid level found for requisition id: {req_id}, level: {level}")
                return {"error": "Invalid level"}
            
            logger.info(f"Level: {level}, Exp Greater Than or Equals: {exp_greater_than_or_equals}, Exp Less Than or Equals: {exp_less_than_or_equals}")

            filtered_result={}
            embeddings = OpenSearchServiceClass(OPENSEARCH_REQUISITION_EMBEDDING_INDEX).get_single_document(req_id)
            result = OpenSearchServiceClass(OPENSEARCH_RC_INDEX).get_similar_candidates(np.array(embeddings["embeddings"]), exp_greater_than_or_equals, exp_less_than_or_equals)

            for candidate_id, candidate_info in result.items():
                total_score = candidate_info.get("total_score", 0)  
                if total_score > float(RECOMMENDED_CANDIDATE_THRESHOLD_SCORE):
                    filtered_result[candidate_id] = candidate_info
                    
            logger.info(f"Getting recommended candidates for req_id completed: {req_id}")
            return filtered_result
      
        except Exception as e:
            logger.error(f"Error in getting similarity: {str(e)}")
            return {"error": str(e)}
